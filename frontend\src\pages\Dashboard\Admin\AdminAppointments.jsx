import { useState, useMemo } from 'react'
import { FiCalendar, <PERSON><PERSON>ser, <PERSON>Eye, FiEdit3, FiTrash2, FiPlus, FiSearch, FiFilter } from 'react-icons/fi'
import AppointmentModal from '../../../components/Modals/AppointmentModal'
import AppointmentDetailModal from '../../../components/Modals/AppointmentDetailModal'
import { adminService } from '../../../services'
import { useDebouncedSearch } from '../../../hooks/useDebounce'
import { searchAppointments } from '../../../utils/searchUtils'
import { useApiWithToast, TOAST_MESSAGES } from '../../../utils/apiWithToast'

const AdminAppointments = ({
  appointments,
  services,
  customers,
  sectionLoading,
  searchTerm,
  setSearchTerm,
  statusFilter,
  setStatusFilter,
  showAddModal,
  setShowAddModal,
  modalType,
  setModalType,
  editingItem,
  setEditingItem,
  setViewingItem,
  setConfirmDialog,
  handleDeleteAppointment,
  branding,
  refreshData,
  refreshAllData
}) => {
  // State for detail modal
  const [showDetailModal, setShowDetailModal] = useState(false)
  const [detailItem, setDetailItem] = useState(null)

  // Toast notifications
  const { executeWithToast } = useApiWithToast()

  // Ensure appointments is always an array - handle backend response structure
  const appointmentsArray = Array.isArray(appointments)
    ? appointments
    : (appointments?.appointments && Array.isArray(appointments.appointments))
      ? appointments.appointments
    : (appointments?.data?.appointments && Array.isArray(appointments.data.appointments))
      ? appointments.data.appointments
    : (appointments?.data && Array.isArray(appointments.data))
        ? appointments.data
        : []

  // Use debounced search for better performance
  const debouncedSearchTerm = useDebouncedSearch(searchTerm, 150)

  // Use memoized filtering for better performance
  const filteredAppointments = useMemo(() => {
    // First apply search filter using the comprehensive search utility
    let filtered = searchAppointments(appointmentsArray, debouncedSearchTerm)

    // Then apply status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(appointment => appointment.status === statusFilter)
    }

    return filtered
  }, [appointmentsArray, debouncedSearchTerm, statusFilter])

  // Handlers for detail modal
  const handleViewDetails = (appointment) => {
    setDetailItem(appointment)
    setShowDetailModal(true)
  }

  const handleEditFromDetail = (appointment) => {
    setShowDetailModal(false)
    setEditingItem(appointment)
    setModalType('edit')
    setShowAddModal(true)
  }

  const handleDeleteFromDetail = async (appointmentId) => {
    await handleDeleteAppointment(appointmentId)
    setShowDetailModal(false)
    // Background refresh is already handled in handleDeleteAppointment
  }

  const handleSaveAppointment = async (appointmentData) => {
    if (editingItem) {
      await executeWithToast(
        () => adminService.updateAppointment(editingItem._id || editingItem.id, appointmentData),
        {
          loadingMessage: 'Updating appointment...',
          successMessage: TOAST_MESSAGES.UPDATE_SUCCESS,
          errorMessage: 'Failed to update appointment',
          onSuccess: () => {
            setShowAddModal(false)
            setEditingItem(null)
            // Trigger background refresh for all admin data
            if (refreshAllData) refreshAllData()
          }
        }
      )
    } else {
      await executeWithToast(
        () => adminService.createAppointment(appointmentData),
        {
          loadingMessage: 'Creating appointment...',
          successMessage: TOAST_MESSAGES.CREATE_SUCCESS,
          errorMessage: 'Failed to create appointment',
          onSuccess: () => {
            setShowAddModal(false)
            setEditingItem(null)
            // Trigger background refresh for all admin data
            if (refreshAllData) refreshAllData()
          }
        }
      )
    }
  }

  const handleEditAppointment = (appointment) => {
    setEditingItem(appointment)
    setModalType('edit')
    setShowAddModal(true)
  }

  const handleDeleteClick = (appointment) => {
    setConfirmDialog({
      title: 'Delete Appointment',
      message: `Are you sure you want to delete the appointment for ${appointment.customerInfo?.name || appointment.user?.name || 'this customer'}?`,
      onConfirm: () => handleDeleteAppointment(appointment._id || appointment.id)
    })
  }

  // Format time helper
  const formatTime = (time) => {
    if (!time) return 'TBD'
    try {
      const [hours, minutes] = time.split(':')
      const hour = parseInt(hours)
      const ampm = hour >= 12 ? 'PM' : 'AM'
      const displayHour = hour % 12 || 12
      return `${displayHour}:${minutes} ${ampm}`
    } catch (error) {
      return time
    }
  }

  // Format date helper
  const formatDate = (dateString) => {
    if (!dateString) return 'TBD'
    try {
      const date = new Date(dateString)
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric'
      })
    } catch (error) {
      return dateString
    }
  }

  // Status badge component
  const StatusBadge = ({ status }) => {
    const statusColors = {
      pending: 'bg-yellow-100 text-yellow-800',
      confirmed: 'bg-green-100 text-green-800',
      cancelled: 'bg-red-100 text-red-800',
      completed: 'bg-blue-100 text-blue-800'
    }

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusColors[status] || 'bg-gray-100 text-gray-800'}`}>
        {status?.charAt(0).toUpperCase() + status?.slice(1) || 'Unknown'}
      </span>
    )
  }

  // Type badge component
  const TypeBadge = ({ type }) => {
    const typeColors = {
      consultation: 'bg-purple-100 text-purple-800',
      service: 'bg-indigo-100 text-indigo-800'
    }

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${typeColors[type] || 'bg-gray-100 text-gray-800'}`}>
        {type?.charAt(0).toUpperCase() + type?.slice(1) || 'Unknown'}
      </span>
    )
  }

  // Payment status badge component
  const PaymentStatusBadge = ({ paymentStatus }) => {
    const paymentColors = {
      pending: 'bg-orange-100 text-orange-800',
      paid: 'bg-green-100 text-green-800',
      refunded: 'bg-red-100 text-red-800'
    }

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${paymentColors[paymentStatus] || 'bg-gray-100 text-gray-800'}`}>
        {paymentStatus?.charAt(0).toUpperCase() + paymentStatus?.slice(1) || 'Pending'}
      </span>
    )
  }

  return (
    <div className="space-y-4 sm:space-y-6 w-full max-w-full overflow-x-hidden">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3 sm:gap-4">
        <div className="min-w-0 flex-1">
          <h2 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 truncate">Appointments Management</h2>
          <p className="text-gray-600 mt-1 text-sm sm:text-base">Manage all customer appointments and bookings</p>
        </div>
        <div className="flex items-center space-x-3 flex-shrink-0">
          <button
            onClick={() => {
              setEditingItem(null)
              setModalType('add')
              setShowAddModal(true)
            }}
            className="flex items-center justify-center px-3 sm:px-4 lg:px-6 py-2 sm:py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 cursor-pointer w-full sm:w-auto text-sm sm:text-base"
          >
            <FiPlus className="w-4 h-4 mr-2" />
            <span className="hidden sm:inline">Add </span>Appointment
          </button>
        </div>
      </div>

      {/* Search and Filter Controls */}
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-4 sm:p-6 shadow-lg border border-white/20 w-full max-w-full">
        <div className="flex flex-col gap-3 sm:gap-4">
          <div className="w-full relative">
            <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4 sm:w-5 sm:h-5" />
            <input
              type="text"
              placeholder="Search appointments..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-9 sm:pl-10 pr-4 py-2.5 sm:py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-sm sm:text-base"
            />
          </div>
          <div className="flex items-center space-x-2 w-full sm:w-auto">
            <FiFilter className="text-gray-400 w-4 h-4 sm:w-5 sm:h-5 flex-shrink-0" />
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="flex-1 sm:flex-none px-3 sm:px-4 py-2.5 sm:py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 cursor-pointer text-sm sm:text-base"
            >
              <option value="all">All Status</option>
              <option value="pending">Pending</option>
              <option value="confirmed">Confirmed</option>
              <option value="cancelled">Cancelled</option>
              <option value="completed">Completed</option>
            </select>
          </div>
        </div>
      </div>

      {/* Appointments List */}
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 overflow-hidden w-full max-w-full">
        {sectionLoading?.appointments ? (
          <div className="overflow-x-auto table-scroll table-scroll-left w-full" style={{ minHeight: '400px' }}>
            <table className="min-w-full divide-y divide-gray-200" style={{ minWidth: '800px' }}>
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ minWidth: '180px' }}>
                    Customer
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ minWidth: '150px' }}>
                    Service
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ minWidth: '140px' }}>
                    Date & Time
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ minWidth: '100px' }}>
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ minWidth: '100px' }}>
                    Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ minWidth: '150px' }}>
                    Contact
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ minWidth: '120px' }}>
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {[...Array(5)].map((_, index) => (
                  <tr key={index}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 w-10 h-10 rounded-full bg-gray-300 animate-pulse"></div>
                        <div className="ml-4">
                          <div className="h-4 bg-gray-300 rounded w-24 animate-pulse mb-1"></div>
                          <div className="h-3 bg-gray-300 rounded w-32 animate-pulse"></div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="h-4 bg-gray-300 rounded w-20 animate-pulse"></div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="h-4 bg-gray-300 rounded w-24 animate-pulse"></div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="h-6 bg-gray-300 rounded-full w-16 animate-pulse"></div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="h-6 bg-gray-300 rounded-full w-20 animate-pulse"></div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="h-4 bg-gray-300 rounded w-32 animate-pulse"></div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right">
                      <div className="flex justify-end space-x-2">
                        <div className="h-8 w-8 bg-gray-300 rounded animate-pulse"></div>
                        <div className="h-8 w-8 bg-gray-300 rounded animate-pulse"></div>
                        <div className="h-8 w-8 bg-gray-300 rounded animate-pulse"></div>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : filteredAppointments.length > 0 ? (
          <>
            {/* Desktop Table Layout */}
            <div className="hidden md:block">
              <div className="overflow-x-auto table-scroll table-scroll-left">
                <table className="min-w-full divide-y divide-gray-200" style={{ minWidth: '800px' }}>
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ minWidth: '180px' }}>
                        Customer
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ minWidth: '150px' }}>
                        Service
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ minWidth: '140px' }}>
                        Date & Time
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ minWidth: '100px' }}>
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ minWidth: '100px' }}>
                        Type
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ minWidth: '150px' }}>
                        Contact
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ minWidth: '120px' }}>
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredAppointments.map((appointment, index) => (
                      <tr key={appointment._id || index} className="hover:bg-gray-50 transition-colors duration-200">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center"
                                 style={{ background: `linear-gradient(135deg, ${branding?.colors?.secondary || '#3b82f6'}20, ${branding?.colors?.accent || '#1d4ed8'}20)` }}>
                              <FiUser className="w-5 h-5" style={{ color: branding?.colors?.secondary || '#3b82f6' }} />
                            </div>
                            <div className="ml-4">
                              <div className="text-sm font-medium text-gray-900">
                                {appointment.customerInfo?.name || appointment.user?.name || 'Unknown Customer'}
                              </div>
                              <div className="text-sm text-gray-500">{appointment.type || 'consultation'}</div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{appointment.service?.name || 'N/A'}</div>
                          <div className="text-sm text-gray-500">
                            {appointment.service?.duration ? `${appointment.service.duration} min` : ''}
                            {appointment.service?.price ? ` • $${appointment.service.price}` : ''}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{formatDate(appointment.date)}</div>
                          <div className="text-sm text-gray-500">{formatTime(appointment.time)}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <StatusBadge status={appointment.status} />
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <TypeBadge type={appointment.type} />
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{appointment.customerInfo?.email || appointment.user?.email}</div>
                          <div className="text-sm text-gray-500">{appointment.customerInfo?.phone || appointment.user?.phone}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <div className="flex justify-end space-x-2">
                            <button
                              onClick={() => handleViewDetails(appointment)}
                              className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors duration-200 cursor-pointer"
                              title="View Details"
                            >
                              <FiEye className="w-4 h-4" />
                            </button>
                            <button
                              onClick={() => handleEditAppointment(appointment)}
                              className="p-2 text-green-600 hover:bg-green-50 rounded-lg transition-colors duration-200 cursor-pointer"
                              title="Edit"
                            >
                              <FiEdit3 className="w-4 h-4" />
                            </button>
                            <button
                              onClick={() => handleDeleteClick(appointment)}
                              className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-200 cursor-pointer"
                              title="Delete"
                            >
                              <FiTrash2 className="w-4 h-4" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>

            {/* Mobile Card Layout */}
            <div className="block md:hidden w-full">
              <div className="space-y-3 p-3 sm:p-4 w-full">
                {filteredAppointments.map((appointment, index) => (
                  <div key={appointment._id || index} className="bg-white/80 backdrop-blur-sm rounded-2xl p-4 sm:p-6 shadow-lg border border-white/20 w-full max-w-full overflow-hidden">
                    <div className="flex items-start justify-between mb-4 gap-3">
                      <div className="flex items-center min-w-0 flex-1">
                        <div className="flex-shrink-0 w-10 h-10 sm:w-12 sm:h-12 rounded-full flex items-center justify-center"
                             style={{ background: `linear-gradient(135deg, ${branding?.colors?.secondary || '#3b82f6'}20, ${branding?.colors?.accent || '#1d4ed8'}20)` }}>
                          <FiUser className="w-5 h-5 sm:w-6 sm:h-6" style={{ color: branding?.colors?.secondary || '#3b82f6' }} />
                        </div>
                        <div className="ml-3 sm:ml-4 min-w-0 flex-1">
                          <div className="font-semibold text-gray-900 text-sm sm:text-base truncate">
                            {appointment.customerInfo?.name || appointment.user?.name || 'Unknown Customer'}
                          </div>
                          <div className="text-xs sm:text-sm text-gray-500 truncate">{appointment.customerInfo?.email || appointment.user?.email}</div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-1 sm:space-x-2 flex-shrink-0">
                        <button
                          onClick={() => handleViewDetails(appointment)}
                          className="p-1.5 sm:p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors duration-200 cursor-pointer"
                          title="View Details"
                        >
                          <FiEye className="w-3.5 h-3.5 sm:w-4 sm:h-4" />
                        </button>
                        <button
                          onClick={() => handleEditAppointment(appointment)}
                          className="p-1.5 sm:p-2 text-green-600 hover:bg-green-50 rounded-lg transition-colors duration-200 cursor-pointer"
                          title="Edit"
                        >
                          <FiEdit3 className="w-3.5 h-3.5 sm:w-4 sm:h-4" />
                        </button>
                        <button
                          onClick={() => handleDeleteClick(appointment)}
                          className="p-1.5 sm:p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-200 cursor-pointer"
                          title="Delete"
                        >
                          <FiTrash2 className="w-3.5 h-3.5 sm:w-4 sm:h-4" />
                        </button>
                      </div>
                    </div>

                    <div className="space-y-2 sm:space-y-3">
                      <div className="flex justify-between items-center gap-2">
                        <span className="text-xs sm:text-sm font-medium text-gray-500 flex-shrink-0">Service:</span>
                        <span className="text-xs sm:text-sm text-gray-900 text-right truncate">{appointment.service?.name || 'N/A'}</span>
                      </div>
                      <div className="flex justify-between items-center gap-2">
                        <span className="text-xs sm:text-sm font-medium text-gray-500 flex-shrink-0">Date:</span>
                        <span className="text-xs sm:text-sm text-gray-900 text-right">{formatDate(appointment.date)}</span>
                      </div>
                      <div className="flex justify-between items-center gap-2">
                        <span className="text-xs sm:text-sm font-medium text-gray-500 flex-shrink-0">Time:</span>
                        <span className="text-xs sm:text-sm text-gray-900 text-right">{formatTime(appointment.time)}</span>
                      </div>
                      <div className="flex justify-between items-center gap-2">
                        <span className="text-xs sm:text-sm font-medium text-gray-500 flex-shrink-0">Status:</span>
                        <StatusBadge status={appointment.status} />
                      </div>
                      <div className="flex justify-between items-center gap-2">
                        <span className="text-xs sm:text-sm font-medium text-gray-500 flex-shrink-0">Type:</span>
                        <TypeBadge type={appointment.type} />
                      </div>
                      <div className="flex justify-between items-center gap-2">
                        <span className="text-xs sm:text-sm font-medium text-gray-500 flex-shrink-0">Payment:</span>
                        <PaymentStatusBadge paymentStatus={appointment.paymentStatus} />
                      </div>
                      {appointment.service?.price && (
                        <div className="flex justify-between items-center gap-2">
                          <span className="text-xs sm:text-sm font-medium text-gray-500 flex-shrink-0">Price:</span>
                          <span className="text-xs sm:text-sm text-gray-900 text-right">${appointment.service.price}</span>
                        </div>
                      )}
                      {appointment.message && (
                        <div className="pt-2 border-t border-gray-200">
                          <span className="text-xs sm:text-sm font-medium text-gray-500">Message:</span>
                          <p className="text-xs sm:text-sm text-gray-900 mt-1 break-words">{appointment.message}</p>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </>
        ) : (
          <div className="text-center py-12">
            <FiCalendar className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No appointments found</h3>
            <p className="text-gray-600 mb-6">
              {searchTerm || statusFilter !== 'all'
                ? 'Try adjusting your search or filter criteria.'
                : 'Get started by adding your first appointment.'
              }
            </p>
          </div>
        )}
      </div>

      {/* Appointment Modal */}
      <AppointmentModal
        isOpen={showAddModal}
        onClose={() => {
          setShowAddModal(false)
          setEditingItem(null)
          setModalType('')
        }}
        onSave={handleSaveAppointment}
        editingItem={editingItem}
        modalType={modalType}
        branding={branding}
        services={services}
        customers={customers}
      />

      {/* Appointment Detail Modal */}
      <AppointmentDetailModal
        appointment={detailItem}
        isOpen={showDetailModal}
        onClose={() => setShowDetailModal(false)}
        onEdit={handleEditFromDetail}
        onDelete={handleDeleteFromDetail}
        services={services}
        customers={customers}
        branding={branding}
      />
    </div>
  )
}

export default AdminAppointments