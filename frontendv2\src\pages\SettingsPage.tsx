import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import Header from '../components/Layout/Header'
import { type User } from '../utils/api'

interface SettingsPageProps {
  currentUser: User | null;
  onLogout: () => void;
}

interface BusinessSettings {
  businessName: string;
  location: string;
  phone: string;
  email: string;
  description: string;
  workingHours: {
    monday: { start: string; end: string; closed: boolean };
    tuesday: { start: string; end: string; closed: boolean };
    wednesday: { start: string; end: string; closed: boolean };
    thursday: { start: string; end: string; closed: boolean };
    friday: { start: string; end: string; closed: boolean };
    saturday: { start: string; end: string; closed: boolean };
    sunday: { start: string; end: string; closed: boolean };
  };
  paymentMethods: {
    cashApp: string;
    zelle: string;
  };
}

export default function SettingsPage({ currentUser, onLogout }: SettingsPageProps) {
  const navigate = useNavigate();
  const [settings, setSettings] = useState<BusinessSettings>({
    businessName: 'dammyspicybeauty',
    location: 'INDIANAPOLIS, IN',
    phone: '(*************',
    email: '<EMAIL>',
    description: 'Thank you for choosing dammyspicybeauty. My name is Dammy I\'m a licenced cosmetologist, I specialize in hair care and beauty treatments of natural hair, microlocs etc.. I\'m located in Indianapolis IN. My main objective is putting smile on individual women faces and getting them more styled up.',
    workingHours: {
      monday: { start: '09:00', end: '17:00', closed: false },
      tuesday: { start: '09:00', end: '17:00', closed: false },
      wednesday: { start: '09:00', end: '17:00', closed: false },
      thursday: { start: '09:00', end: '17:00', closed: false },
      friday: { start: '09:00', end: '17:00', closed: false },
      saturday: { start: '10:00', end: '16:00', closed: false },
      sunday: { start: '10:00', end: '16:00', closed: true }
    },
    paymentMethods: {
      cashApp: '(*************',
      zelle: '(*************'
    }
  });
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<'general' | 'hours' | 'payment'>('general');

  useEffect(() => {
    if (!currentUser || currentUser.role !== 'admin') {
      navigate('/');
      return;
    }
    
    // TODO: Fetch settings from backend
    fetchSettings();
  }, [currentUser, navigate]);

  const fetchSettings = async () => {
    try {
      // TODO: Replace with actual API call
      // const response = await settingsAPI.getSettings();
      // setSettings(response);
    } catch (error) {
      console.error('Error fetching settings:', error);
    }
  };

  const handleSaveSettings = async () => {
    try {
      setLoading(true);
      // TODO: Save settings via API
      // await settingsAPI.updateSettings(settings);
      
      alert('Settings saved successfully!');
    } catch (error) {
      console.error('Error saving settings:', error);
      alert('Failed to save settings. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleWorkingHoursChange = (day: string, field: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      workingHours: {
        ...prev.workingHours,
        [day]: {
          ...prev.workingHours[day as keyof typeof prev.workingHours],
          [field]: value
        }
      }
    }));
  };

  const handlePaymentMethodChange = (method: string, value: string) => {
    setSettings(prev => ({
      ...prev,
      paymentMethods: {
        ...prev.paymentMethods,
        [method]: value
      }
    }));
  };

  const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];

  return (
    <div className="app">
      <Header currentUser={currentUser} onLogout={onLogout} />
      
      <main className="main-content">
        <div className="page-header">
          <h1>Settings</h1>
          <button 
            className="btn-primary"
            onClick={handleSaveSettings}
            disabled={loading}
          >
            {loading ? 'Saving...' : 'Save Settings'}
          </button>
        </div>

        <div className="settings-tabs">
          <button
            className={`settings-tab ${activeTab === 'general' ? 'active' : ''}`}
            onClick={() => setActiveTab('general')}
          >
            General
          </button>
          <button
            className={`settings-tab ${activeTab === 'hours' ? 'active' : ''}`}
            onClick={() => setActiveTab('hours')}
          >
            Working Hours
          </button>
          <button
            className={`settings-tab ${activeTab === 'payment' ? 'active' : ''}`}
            onClick={() => setActiveTab('payment')}
          >
            Payment Methods
          </button>
        </div>

        {activeTab === 'general' && (
          <div className="settings-section">
            <h2>General Settings</h2>
            <div className="settings-form">
              <div className="form-group">
                <label>Business Name</label>
                <input
                  type="text"
                  value={settings.businessName}
                  onChange={(e) => handleInputChange('businessName', e.target.value)}
                />
              </div>
              
              <div className="form-group">
                <label>Location</label>
                <input
                  type="text"
                  value={settings.location}
                  onChange={(e) => handleInputChange('location', e.target.value)}
                />
              </div>
              
              <div className="form-group">
                <label>Phone</label>
                <input
                  type="text"
                  value={settings.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                />
              </div>
              
              <div className="form-group">
                <label>Email</label>
                <input
                  type="email"
                  value={settings.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                />
              </div>
              
              <div className="form-group">
                <label>Description</label>
                <textarea
                  value={settings.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  rows={4}
                />
              </div>
            </div>
          </div>
        )}

        {activeTab === 'hours' && (
          <div className="settings-section">
            <h2>Working Hours</h2>
            <div className="working-hours-form">
              {days.map(day => (
                <div key={day} className="day-hours">
                  <div className="day-name">
                    {day.charAt(0).toUpperCase() + day.slice(1)}
                  </div>
                  <div className="hours-controls">
                    <label>
                      <input
                        type="checkbox"
                        checked={!settings.workingHours[day as keyof typeof settings.workingHours].closed}
                        onChange={(e) => handleWorkingHoursChange(day, 'closed', !e.target.checked)}
                      />
                      Open
                    </label>
                    {!settings.workingHours[day as keyof typeof settings.workingHours].closed && (
                      <>
                        <input
                          type="time"
                          value={settings.workingHours[day as keyof typeof settings.workingHours].start}
                          onChange={(e) => handleWorkingHoursChange(day, 'start', e.target.value)}
                        />
                        <span>to</span>
                        <input
                          type="time"
                          value={settings.workingHours[day as keyof typeof settings.workingHours].end}
                          onChange={(e) => handleWorkingHoursChange(day, 'end', e.target.value)}
                        />
                      </>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'payment' && (
          <div className="settings-section">
            <h2>Payment Methods</h2>
            <div className="settings-form">
              <div className="form-group">
                <label>Cash App</label>
                <input
                  type="text"
                  value={settings.paymentMethods.cashApp}
                  onChange={(e) => handlePaymentMethodChange('cashApp', e.target.value)}
                  placeholder="Cash App phone number or username"
                />
              </div>
              
              <div className="form-group">
                <label>Zelle</label>
                <input
                  type="text"
                  value={settings.paymentMethods.zelle}
                  onChange={(e) => handlePaymentMethodChange('zelle', e.target.value)}
                  placeholder="Zelle phone number or email"
                />
              </div>
            </div>
          </div>
        )}
      </main>
    </div>
  )
}
