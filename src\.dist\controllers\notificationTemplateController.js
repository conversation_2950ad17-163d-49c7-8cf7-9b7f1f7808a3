"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationTemplateController = void 0;
const models_1 = require("../models");
const response_1 = require("../utils/response");
class NotificationTemplateController {
    static async getNotificationTemplate(req, res) {
        try {
            const { type, channel } = req.params;
            const template = await models_1.NotificationTemplate.findOne({
                type,
                channel,
                isActive: true
            });
            if (!template) {
                (0, response_1.sendNotFound)(res, 'Notification template not found');
                return;
            }
            (0, response_1.sendSuccess)(res, 'Notification template retrieved successfully', template);
        }
        catch (error) {
            console.error('Get notification template error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async updateNotificationTemplate(req, res) {
        try {
            const { type, channel } = req.params;
            const { subject, content, variables, isActive } = req.body;
            let template = await models_1.NotificationTemplate.findOne({ type, channel });
            if (!template) {
                // Create new template
                template = await models_1.NotificationTemplate.create({
                    type,
                    channel,
                    subject,
                    content,
                    variables: variables || [],
                    isActive: isActive !== undefined ? isActive : true
                });
            }
            else {
                // Update existing template
                if (subject !== undefined)
                    template.subject = subject;
                if (content !== undefined)
                    template.content = content;
                if (variables !== undefined)
                    template.variables = variables;
                if (isActive !== undefined)
                    template.isActive = isActive;
                await template.save();
            }
            (0, response_1.sendSuccess)(res, 'Notification template updated successfully', template);
        }
        catch (error) {
            console.error('Update notification template error:', error);
            if (error.code === 11000) {
                (0, response_1.sendError)(res, 'Notification template for this type and channel already exists');
            }
            else {
                (0, response_1.sendError)(res, error.message);
            }
        }
    }
    static async getAllNotificationTemplates(req, res) {
        try {
            const { type, channel, active } = req.query;
            const filter = {};
            if (type)
                filter.type = type;
            if (channel)
                filter.channel = channel;
            if (active !== undefined)
                filter.isActive = active === 'true';
            const templates = await models_1.NotificationTemplate.find(filter)
                .sort({ type: 1, channel: 1 });
            (0, response_1.sendSuccess)(res, 'Notification templates retrieved successfully', templates);
        }
        catch (error) {
            console.error('Get all notification templates error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async deleteNotificationTemplate(req, res) {
        try {
            const { type, channel } = req.params;
            const template = await models_1.NotificationTemplate.findOneAndDelete({ type, channel });
            if (!template) {
                (0, response_1.sendNotFound)(res, 'Notification template not found');
                return;
            }
            (0, response_1.sendSuccess)(res, 'Notification template deleted successfully');
        }
        catch (error) {
            console.error('Delete notification template error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
}
exports.NotificationTemplateController = NotificationTemplateController;
