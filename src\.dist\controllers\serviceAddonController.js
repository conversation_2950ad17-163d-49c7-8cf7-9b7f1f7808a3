"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ServiceAddonController = void 0;
const models_1 = require("../models");
const response_1 = require("../utils/response");
class ServiceAddonController {
    static async getServiceAddons(req, res) {
        try {
            const { id } = req.params; // Service ID
            const { active } = req.query;
            // Verify service exists
            const service = await models_1.Service.findById(id);
            if (!service) {
                (0, response_1.sendNotFound)(res, 'Service not found');
                return;
            }
            const filter = { service: id };
            if (active !== undefined) {
                filter.isActive = active === 'true';
            }
            const addons = await models_1.ServiceAddon.find(filter).sort({ name: 1 });
            (0, response_1.sendSuccess)(res, 'Service add-ons retrieved successfully', addons);
        }
        catch (error) {
            console.error('Get service add-ons error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async createServiceAddon(req, res) {
        try {
            const { id } = req.params; // Service ID
            const addonData = { ...req.body, service: id };
            // Verify service exists
            const service = await models_1.Service.findById(id);
            if (!service) {
                (0, response_1.sendNotFound)(res, 'Service not found');
                return;
            }
            const addon = await models_1.ServiceAddon.create(addonData);
            (0, response_1.sendCreated)(res, 'Service add-on created successfully', addon);
        }
        catch (error) {
            console.error('Create service add-on error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async updateServiceAddon(req, res) {
        try {
            const { addonId } = req.params;
            const updateData = req.body;
            const addon = await models_1.ServiceAddon.findByIdAndUpdate(addonId, updateData, { new: true, runValidators: true });
            if (!addon) {
                (0, response_1.sendNotFound)(res, 'Service add-on not found');
                return;
            }
            (0, response_1.sendSuccess)(res, 'Service add-on updated successfully', addon);
        }
        catch (error) {
            console.error('Update service add-on error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async deleteServiceAddon(req, res) {
        try {
            const { addonId } = req.params;
            const addon = await models_1.ServiceAddon.findByIdAndDelete(addonId);
            if (!addon) {
                (0, response_1.sendNotFound)(res, 'Service add-on not found');
                return;
            }
            (0, response_1.sendSuccess)(res, 'Service add-on deleted successfully');
        }
        catch (error) {
            console.error('Delete service add-on error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
}
exports.ServiceAddonController = ServiceAddonController;
