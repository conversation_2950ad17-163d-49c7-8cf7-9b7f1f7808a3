import { useState, useEffect } from 'react';
import { appointmentAPI } from '../../utils/appointmentAPI';
import {
  type User,
  dashboardAPI,
  clearAuthData,
  type DashboardData
} from '../../utils/api';

interface UserDashboardProps {
  currentUser: User | null;
  onLogout: () => void;
  onBookNew: () => void;
}

export default function UserDashboard({ currentUser, onLogout, onBookNew }: UserDashboardProps) {
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [activeTab, setActiveTab] = useState<'upcoming' | 'past' | 'profile'>('upcoming');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (currentUser) {
      fetchDashboardData();
    }
  }, [currentUser]);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Try to use the new dashboard API first
      try {
        const response = await dashboardAPI.getUserDashboard();
        console.log('Dashboard API response:', response);

        if (response && response.success && response.data) {
          setDashboardData(response.data);
        } else {
          throw new Error('Invalid dashboard response format');
        }
      } catch (dashboardError) {
        console.warn('Dashboard API failed, trying appointment API fallback:', dashboardError);

        try {
          const response = await appointmentAPI.getUserAppointments();
          console.log('Appointment API fallback response:', response);

          // Convert old format to new dashboard format
          let appointmentsData: any[] = [];
          if (Array.isArray(response)) {
            appointmentsData = response;
          }

          // Create a basic dashboard data structure from appointments
          const fallbackDashboardData: DashboardData = {
            user: currentUser || { id: '', firstName: 'User', lastName: '', email: '', phone: '', role: 'user', createdAt: '' },
            appointments: {
              all: appointmentsData,
              recent: appointmentsData.slice(0, 5),
              upcoming: appointmentsData.filter((apt: any) =>
                new Date(apt.date) >= new Date() && apt.status !== 'cancelled'
              )
            },
            statistics: {
              appointments: {
                total: appointmentsData.length,
                pending: appointmentsData.filter((apt: any) => apt.status === 'pending').length,
                confirmed: appointmentsData.filter((apt: any) => apt.status === 'confirmed').length,
                completed: appointmentsData.filter((apt: any) => apt.status === 'completed').length,
                cancelled: appointmentsData.filter((apt: any) => apt.status === 'cancelled').length,
              },
              totalSpent: appointmentsData.reduce((sum: number, apt: any) => sum + (apt.totalPrice || apt.servicePrice || 0), 0),
              favoriteServices: [],
              memberSince: currentUser?.createdAt || '',
              lastActivity: currentUser?.updatedAt || ''
            }
          };

          setDashboardData(fallbackDashboardData);
        } catch (appointmentError) {
          console.error('Both APIs failed:', { dashboardError, appointmentError });
          setError('Failed to load dashboard data. Please try again.');
        }
      }
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      setError('An unexpected error occurred while loading your dashboard.');
    } finally {
      setLoading(false);
    }
  };

  const handleCancelAppointment = async (appointmentId: string) => {
    if (window.confirm('Are you sure you want to cancel this appointment?')) {
      try {
        await appointmentAPI.cancelAppointment(appointmentId);
        await fetchDashboardData(); // Refresh the dashboard data
        alert('Appointment cancelled successfully');
      } catch (error) {
        console.error('Error cancelling appointment:', error);
        alert('Failed to cancel appointment. Please try again.');
      }
    }
  };

  const handleLogout = () => {
    clearAuthData();
    onLogout();
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // formatTime function removed as it was unused

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed': return '#28a745';
      case 'pending': return '#ffc107';
      case 'completed': return '#6c757d';
      case 'cancelled': return '#dc3545';
      default: return '#6c757d';
    }
  };

  // Extract data from dashboard or use empty defaults
  const appointments = dashboardData?.appointments || { all: [], recent: [], upcoming: [] };
  const statistics = dashboardData?.statistics || {
    appointments: { total: 0, pending: 0, confirmed: 0, completed: 0, cancelled: 0 },
    totalSpent: 0,
    favoriteServices: [],
    memberSince: '',
    lastActivity: ''
  };
  const user = dashboardData?.user || currentUser;

  const upcomingAppointments = appointments.upcoming || [];
  const pastAppointments = appointments.all.filter(apt =>
    new Date(apt.date) < new Date() || apt.status === 'completed' || apt.status === 'cancelled'
  ) || [];

  if (!currentUser) {
    return <div>Please log in to view your dashboard.</div>;
  }

  if (loading) {
    return (
      <div className="dashboard-container">
        <div className="loading-container">
          <p>Loading your dashboard...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="dashboard-container">
        <div className="error-container">
          <h3>Error Loading Dashboard</h3>
          <p>{error}</p>
          <button onClick={fetchDashboardData} className="action-button primary">
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="dashboard-container">
      {/* Header */}
      <header className="dashboard-header">
        <div className="dashboard-header-content">
          <div className="dashboard-logo">
            <h1>dammyspicybeauty</h1>
          </div>
          <div className="dashboard-user-menu">
            <span className="welcome-text">Welcome, {currentUser.firstName || currentUser.name?.split(' ')[0] || 'User'}!</span>
            <button className="logout-button" onClick={handleLogout}>
              Logout
            </button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="dashboard-main">
        <div className="dashboard-content">
          {/* Quick Actions */}
          <div className="quick-actions">
            <button className="action-button primary" onClick={onBookNew}>
              📅 Book New Appointment
            </button>
            <div className="stats-grid">
              <div className="stat-card">
                <h3>{statistics.appointments.total}</h3>
                <p>Total Appointments</p>
              </div>
              <div className="stat-card">
                <h3>{upcomingAppointments.length}</h3>
                <p>Upcoming</p>
              </div>
              <div className="stat-card">
                <h3>{statistics.appointments.pending}</h3>
                <p>Pending</p>
              </div>
              <div className="stat-card">
                <h3>${statistics.totalSpent.toFixed(2)}</h3>
                <p>Total Spent</p>
              </div>
            </div>
          </div>

          {/* Navigation Tabs */}
          <div className="dashboard-tabs">
            <button 
              className={`tab-button ${activeTab === 'upcoming' ? 'active' : ''}`}
              onClick={() => setActiveTab('upcoming')}
            >
              Upcoming Appointments ({upcomingAppointments.length})
            </button>
            <button 
              className={`tab-button ${activeTab === 'past' ? 'active' : ''}`}
              onClick={() => setActiveTab('past')}
            >
              Past Appointments ({pastAppointments.length})
            </button>
            <button 
              className={`tab-button ${activeTab === 'profile' ? 'active' : ''}`}
              onClick={() => setActiveTab('profile')}
            >
              Profile
            </button>
          </div>

          {/* Tab Content */}
          <div className="tab-content">
            {activeTab === 'upcoming' && (
              <div className="appointments-list">
                {upcomingAppointments.length === 0 ? (
                  <div className="empty-state">
                    <h3>No upcoming appointments</h3>
                    <p>Book your next appointment to get started!</p>
                    <button className="action-button primary" onClick={onBookNew}>
                      Book Appointment
                    </button>
                  </div>
                ) : (
                  upcomingAppointments.map(appointment => (
                    <div key={appointment.id} className="appointment-card">
                      <div className="appointment-header">
                        <h4>{appointment.serviceName}</h4>
                        <span
                          className="status-badge"
                          style={{ backgroundColor: getStatusColor(appointment.status) }}
                        >
                          {appointment.status.toUpperCase()}
                        </span>
                      </div>
                      <div className="appointment-details">
                        <p><strong>Date:</strong> {formatDate(appointment.date)}</p>
                        <p><strong>Time:</strong> {appointment.time}</p>
                        <p><strong>Price:</strong> ${appointment.servicePrice.toFixed(2)}</p>
                        {appointment.serviceCategory && (
                          <p><strong>Category:</strong> {appointment.serviceCategory}</p>
                        )}
                        {appointment.serviceDuration && (
                          <p><strong>Duration:</strong> {appointment.serviceDuration} minutes</p>
                        )}
                        {appointment.notes && (
                          <div className="appointment-notes">
                            <strong>Notes:</strong> {appointment.notes}
                          </div>
                        )}
                      </div>
                      <div className="appointment-actions">
                        {appointment.status === 'pending' && (
                          <button
                            className="action-button danger"
                            onClick={() => handleCancelAppointment(appointment.id)}
                          >
                            Cancel Appointment
                          </button>
                        )}
                      </div>
                    </div>
                  ))
                )}
              </div>
            )}

            {activeTab === 'past' && (
              <div className="appointments-list">
                {pastAppointments.length === 0 ? (
                  <div className="empty-state">
                    <h3>No past appointments</h3>
                    <p>Your appointment history will appear here.</p>
                  </div>
                ) : (
                  pastAppointments.map(appointment => (
                    <div key={appointment.id} className="appointment-card past">
                      <div className="appointment-header">
                        <h4>{appointment.serviceName}</h4>
                        <span
                          className="status-badge"
                          style={{ backgroundColor: getStatusColor(appointment.status) }}
                        >
                          {appointment.status.toUpperCase()}
                        </span>
                      </div>
                      <div className="appointment-details">
                        <p><strong>Date:</strong> {formatDate(appointment.date)}</p>
                        <p><strong>Time:</strong> {appointment.time}</p>
                        <p><strong>Price:</strong> ${appointment.servicePrice.toFixed(2)}</p>
                        {appointment.serviceCategory && (
                          <p><strong>Category:</strong> {appointment.serviceCategory}</p>
                        )}
                        {appointment.serviceDuration && (
                          <p><strong>Duration:</strong> {appointment.serviceDuration} minutes</p>
                        )}
                        {appointment.notes && (
                          <div className="appointment-notes">
                            <strong>Notes:</strong> {appointment.notes}
                          </div>
                        )}
                      </div>
                    </div>
                  ))
                )}
              </div>
            )}

            {activeTab === 'profile' && (
              <div className="profile-section">
                <div className="profile-card">
                  <h3>Personal Information</h3>
                  <div className="profile-info">
                    <div className="info-row">
                      <label>Name:</label>
                      <span>{user?.name || `${user?.firstName || ''} ${user?.lastName || ''}`.trim()}</span>
                    </div>
                    <div className="info-row">
                      <label>Email:</label>
                      <span>{user?.email}</span>
                    </div>
                    <div className="info-row">
                      <label>Phone:</label>
                      <span>{user?.phone || 'Not provided'}</span>
                    </div>
                    <div className="info-row">
                      <label>Role:</label>
                      <span>{user?.role}</span>
                    </div>
                    <div className="info-row">
                      <label>Verified:</label>
                      <span>{user?.isVerified ? 'Yes' : 'No'}</span>
                    </div>
                    <div className="info-row">
                      <label>Member Since:</label>
                      <span>{statistics.memberSince ? formatDate(statistics.memberSince) : 'N/A'}</span>
                    </div>
                    <div className="info-row">
                      <label>Last Activity:</label>
                      <span>{statistics.lastActivity ? formatDate(statistics.lastActivity) : 'N/A'}</span>
                    </div>
                  </div>
                  <button className="action-button secondary">
                    Edit Profile
                  </button>
                </div>

                {/* Favorite Services */}
                {statistics.favoriteServices.length > 0 && (
                  <div className="profile-card">
                    <h3>Favorite Services</h3>
                    <div className="favorite-services">
                      {statistics.favoriteServices.map(service => (
                        <div key={service.id} className="favorite-service">
                          <div className="service-info">
                            <h4>{service.name}</h4>
                            <p>Category: {service.category}</p>
                            <p>Price: ${service.price}</p>
                            <p>Booked {service.count} time{service.count !== 1 ? 's' : ''}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Notification Preferences */}
                {user?.notificationPreferences && (
                  <div className="profile-card">
                    <h3>Notification Preferences</h3>
                    <div className="notification-prefs">
                      <div className="pref-row">
                        <label>Email Notifications:</label>
                        <span>{user.notificationPreferences.email ? 'Enabled' : 'Disabled'}</span>
                      </div>
                      <div className="pref-row">
                        <label>SMS Notifications:</label>
                        <span>{user.notificationPreferences.sms ? 'Enabled' : 'Disabled'}</span>
                      </div>
                      <div className="pref-row">
                        <label>Push Notifications:</label>
                        <span>{user.notificationPreferences.push ? 'Enabled' : 'Disabled'}</span>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </main>
    </div>
  );
}
