"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TestimonialController = void 0;
const models_1 = require("../models");
const response_1 = require("../utils/response");
class TestimonialController {
    static async getTestimonials(req, res) {
        try {
            const { featured, page = 1, limit = 10 } = req.query;
            const filter = { isActive: true };
            if (featured !== undefined) {
                filter.featured = featured === 'true';
            }
            const pageNum = Number(page);
            const limitNum = Number(limit);
            const skip = (pageNum - 1) * limitNum;
            const [testimonials, total] = await Promise.all([
                models_1.Testimonial.find(filter)
                    .sort({ featured: -1, rating: -1, createdAt: -1 })
                    .skip(skip)
                    .limit(limitNum),
                models_1.Testimonial.countDocuments(filter)
            ]);
            const totalPages = Math.ceil(total / limitNum);
            (0, response_1.sendSuccess)(res, 'Testimonials retrieved successfully', {
                testimonials,
                pagination: {
                    currentPage: pageNum,
                    totalPages,
                    totalItems: total,
                    itemsPerPage: limitNum,
                    hasNextPage: pageNum < totalPages,
                    hasPrevPage: pageNum > 1
                }
            });
        }
        catch (error) {
            console.error('Get testimonials error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async createTestimonial(req, res) {
        try {
            const testimonialData = req.body;
            const testimonial = await models_1.Testimonial.create(testimonialData);
            (0, response_1.sendCreated)(res, 'Testimonial created successfully', testimonial);
        }
        catch (error) {
            console.error('Create testimonial error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async updateTestimonial(req, res) {
        try {
            const { id } = req.params;
            const updateData = req.body;
            const testimonial = await models_1.Testimonial.findByIdAndUpdate(id, updateData, { new: true, runValidators: true });
            if (!testimonial) {
                (0, response_1.sendNotFound)(res, 'Testimonial not found');
                return;
            }
            (0, response_1.sendSuccess)(res, 'Testimonial updated successfully', testimonial);
        }
        catch (error) {
            console.error('Update testimonial error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async deleteTestimonial(req, res) {
        try {
            const { id } = req.params;
            const testimonial = await models_1.Testimonial.findByIdAndDelete(id);
            if (!testimonial) {
                (0, response_1.sendNotFound)(res, 'Testimonial not found');
                return;
            }
            (0, response_1.sendSuccess)(res, 'Testimonial deleted successfully');
        }
        catch (error) {
            console.error('Delete testimonial error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async getTestimonialById(req, res) {
        try {
            const { id } = req.params;
            const testimonial = await models_1.Testimonial.findById(id);
            if (!testimonial) {
                (0, response_1.sendNotFound)(res, 'Testimonial not found');
                return;
            }
            (0, response_1.sendSuccess)(res, 'Testimonial retrieved successfully', testimonial);
        }
        catch (error) {
            console.error('Get testimonial by ID error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
}
exports.TestimonialController = TestimonialController;
