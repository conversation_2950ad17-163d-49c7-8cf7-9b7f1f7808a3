"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.StaffController = void 0;
const models_1 = require("../models");
const response_1 = require("../utils/response");
class StaffController {
    static async getAllStaff(req, res) {
        try {
            const { active, page = 1, limit = 20 } = req.query;
            const filter = {};
            if (active !== undefined) {
                filter.isActive = active === 'true';
            }
            const pageNum = Number(page);
            const limitNum = Number(limit);
            const skip = (pageNum - 1) * limitNum;
            const [staff, total] = await Promise.all([
                models_1.Staff.find(filter)
                    .populate('services', 'name price duration')
                    .sort({ name: 1 })
                    .skip(skip)
                    .limit(limitNum),
                models_1.Staff.countDocuments(filter)
            ]);
            const totalPages = Math.ceil(total / limitNum);
            (0, response_1.sendSuccess)(res, 'Staff retrieved successfully', {
                staff,
                pagination: {
                    currentPage: pageNum,
                    totalPages,
                    totalItems: total,
                    itemsPerPage: limitNum,
                    hasNextPage: pageNum < totalPages,
                    hasPrevPage: pageNum > 1
                }
            });
        }
        catch (error) {
            console.error('Get all staff error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async getStaffById(req, res) {
        try {
            const { id } = req.params;
            const staff = await models_1.Staff.findById(id)
                .populate('services', 'name price duration category');
            if (!staff) {
                (0, response_1.sendNotFound)(res, 'Staff member not found');
                return;
            }
            (0, response_1.sendSuccess)(res, 'Staff member retrieved successfully', staff);
        }
        catch (error) {
            console.error('Get staff by ID error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async createStaff(req, res) {
        try {
            const staffData = req.body;
            const staff = await models_1.Staff.create(staffData);
            await staff.populate('services', 'name price duration');
            (0, response_1.sendCreated)(res, 'Staff member created successfully', staff);
        }
        catch (error) {
            console.error('Create staff error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async updateStaff(req, res) {
        try {
            const { id } = req.params;
            const updateData = req.body;
            const staff = await models_1.Staff.findByIdAndUpdate(id, updateData, { new: true, runValidators: true }).populate('services', 'name price duration');
            if (!staff) {
                (0, response_1.sendNotFound)(res, 'Staff member not found');
                return;
            }
            (0, response_1.sendSuccess)(res, 'Staff member updated successfully', staff);
        }
        catch (error) {
            console.error('Update staff error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async deleteStaff(req, res) {
        try {
            const { id } = req.params;
            const staff = await models_1.Staff.findByIdAndDelete(id);
            if (!staff) {
                (0, response_1.sendNotFound)(res, 'Staff member not found');
                return;
            }
            (0, response_1.sendSuccess)(res, 'Staff member deleted successfully');
        }
        catch (error) {
            console.error('Delete staff error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async getStaffAvailability(req, res) {
        try {
            const { id } = req.params;
            const { date } = req.query;
            const staff = await models_1.Staff.findById(id);
            if (!staff) {
                (0, response_1.sendNotFound)(res, 'Staff member not found');
                return;
            }
            let availability = staff.availability;
            // If date is provided, filter by day of week
            if (date) {
                const requestedDate = new Date(date);
                const dayOfWeek = requestedDate.toLocaleDateString('en-US', { weekday: 'long' });
                availability = staff.availability.filter(avail => avail.day === dayOfWeek);
            }
            (0, response_1.sendSuccess)(res, 'Staff availability retrieved successfully', {
                staffId: staff._id,
                name: staff.name,
                availability
            });
        }
        catch (error) {
            console.error('Get staff availability error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async updateStaffAvailability(req, res) {
        try {
            const { id } = req.params;
            const { availability } = req.body;
            const staff = await models_1.Staff.findByIdAndUpdate(id, { availability }, { new: true, runValidators: true });
            if (!staff) {
                (0, response_1.sendNotFound)(res, 'Staff member not found');
                return;
            }
            (0, response_1.sendSuccess)(res, 'Staff availability updated successfully', staff.availability);
        }
        catch (error) {
            console.error('Update staff availability error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
}
exports.StaffController = StaffController;
