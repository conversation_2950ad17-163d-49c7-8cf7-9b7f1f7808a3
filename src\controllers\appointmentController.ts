import { Request, Response } from 'express';
import { AppointmentService } from '../services';
import { sendSuccess, sendError, sendCreated, sendNotFound } from '../utils/response';
import { AuthenticatedRequest, AppointmentQuery } from '../types';

export class AppointmentController {
  static async getAvailability(req: Request, res: Response): Promise<void> {
    try {
      const { date, service } = req.query;

      if (!date) {
        sendError(res, 'Date parameter is required');
        return;
      }

      const availableSlots = await AppointmentService.getAvailableTimeSlots(
        date as string,
        service as string
      );

      sendSuccess(res, 'Available time slots retrieved successfully', {
        date,
        availableSlots
      });
    } catch (error) {
      console.error('Get availability error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async createAppointment(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        sendError(res, 'Authentication required', undefined, 401);
        return;
      }

      const { service, date, time, name, email, phone, message } = req.body;

      const appointment = await AppointmentService.createAppointment({
        user: req.user._id,
        service,
        date: new Date(date),
        time,
        customerInfo: {
          name,
          email,
          phone
        },
        message,
        type: 'service'
      });

      sendCreated(res, 'Appointment created successfully', appointment);
    } catch (error) {
      console.error('Create appointment error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async getUserAppointments(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        sendError(res, 'Authentication required', undefined, 401);
        return;
      }

      const { page, limit } = req.query;
      const pageNum = page ? parseInt(page as string) : undefined;
      const limitNum = limit ? parseInt(limit as string) : undefined;

      const result = await AppointmentService.getUserAppointments(req.user._id, pageNum, limitNum);

      sendSuccess(res, 'User appointments retrieved successfully', result);
    } catch (error) {
      console.error('Get user appointments error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async updateAppointment(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        sendError(res, 'Authentication required', undefined, 401);
        return;
      }

      const { id } = req.params;
      const updateData = req.body;

      const appointment = await AppointmentService.updateAppointment(
        id,
        req.user._id,
        updateData
      );

      sendSuccess(res, 'Appointment updated successfully', appointment);
    } catch (error) {
      console.error('Update appointment error:', error);
      if ((error as Error).message === 'Appointment not found') {
        sendNotFound(res, 'Appointment not found');
      } else {
        sendError(res, (error as Error).message);
      }
    }
  }

  static async cancelAppointment(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        sendError(res, 'Authentication required', undefined, 401);
        return;
      }

      const { id } = req.params;

      await AppointmentService.cancelAppointment(id, req.user._id);

      sendSuccess(res, 'Appointment cancelled successfully');
    } catch (error) {
      console.error('Cancel appointment error:', error);
      if ((error as Error).message === 'Appointment not found') {
        sendNotFound(res, 'Appointment not found');
      } else {
        sendError(res, (error as Error).message);
      }
    }
  }

  static async getAllAppointments(req: Request, res: Response): Promise<void> {
    try {
      const query: AppointmentQuery = {
        date: req.query.date as string,
        status: req.query.status as string,
        service: req.query.service as string
      };

      const appointments = await AppointmentService.getAppointments(query);

      sendSuccess(res, 'Appointments retrieved successfully', appointments);
    } catch (error) {
      console.error('Get all appointments error:', error);
      sendError(res, (error as Error).message);
    }
  }
}
