"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReviewController = void 0;
const models_1 = require("../models");
const reviewService_1 = require("../services/reviewService");
const response_1 = require("../utils/response");
class ReviewController {
    static async getProductReviews(req, res) {
        try {
            const { id } = req.params;
            const { page = 1, limit = 10, sortBy = 'createdAt', sortOrder = 'desc' } = req.query;
            // Verify product exists
            const product = await models_1.Product.findById(id);
            if (!product) {
                (0, response_1.sendNotFound)(res, 'Product not found');
                return;
            }
            const pageNum = Number(page);
            const limitNum = Number(limit);
            const skip = (pageNum - 1) * limitNum;
            // Sort
            const sort = {};
            sort[sortBy] = sortOrder === 'desc' ? -1 : 1;
            const [reviews, total] = await Promise.all([
                models_1.Review.find({ product: id })
                    .populate('user', 'name')
                    .sort(sort)
                    .skip(skip)
                    .limit(limitNum),
                models_1.Review.countDocuments({ product: id })
            ]);
            const totalPages = Math.ceil(total / limitNum);
            // Calculate rating statistics
            const ratingStats = await models_1.Review.aggregate([
                { $match: { product: product._id } },
                {
                    $group: {
                        _id: '$rating',
                        count: { $sum: 1 }
                    }
                },
                { $sort: { _id: -1 } }
            ]);
            const averageRating = await models_1.Review.aggregate([
                { $match: { product: product._id } },
                {
                    $group: {
                        _id: null,
                        average: { $avg: '$rating' },
                        total: { $sum: 1 }
                    }
                }
            ]);
            (0, response_1.sendSuccess)(res, 'Product reviews retrieved successfully', {
                reviews,
                pagination: {
                    currentPage: pageNum,
                    totalPages,
                    totalItems: total,
                    itemsPerPage: limitNum,
                    hasNextPage: pageNum < totalPages,
                    hasPrevPage: pageNum > 1
                },
                statistics: {
                    averageRating: averageRating[0]?.average || 0,
                    totalReviews: averageRating[0]?.total || 0,
                    ratingDistribution: ratingStats
                }
            });
        }
        catch (error) {
            console.error('Get product reviews error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async createReview(req, res) {
        try {
            if (!req.user) {
                (0, response_1.sendError)(res, 'Authentication required', undefined, 401);
                return;
            }
            const { product, service, rating, title, comment } = req.body;
            // Verify product or service exists
            if (product) {
                const productExists = await models_1.Product.findById(product);
                if (!productExists) {
                    (0, response_1.sendNotFound)(res, 'Product not found');
                    return;
                }
            }
            else if (service) {
                const serviceExists = await models_1.Service.findById(service);
                if (!serviceExists) {
                    (0, response_1.sendNotFound)(res, 'Service not found');
                    return;
                }
            }
            else {
                (0, response_1.sendError)(res, 'Either product or service must be specified');
                return;
            }
            const review = await reviewService_1.ReviewService.createReview({
                user: req.user._id,
                product,
                service,
                rating,
                title,
                comment
            });
            (0, response_1.sendCreated)(res, 'Review submitted successfully and is pending approval', review);
        }
        catch (error) {
            console.error('Create review error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async updateReview(req, res) {
        try {
            if (!req.user) {
                (0, response_1.sendError)(res, 'Authentication required', undefined, 401);
                return;
            }
            const { id } = req.params;
            const { rating, comment } = req.body;
            const review = await models_1.Review.findOne({
                _id: id,
                user: req.user._id
            });
            if (!review) {
                (0, response_1.sendNotFound)(res, 'Review not found');
                return;
            }
            review.rating = rating;
            review.comment = comment;
            await review.save();
            await review.populate('user', 'name');
            // Update product rating
            const reviews = await models_1.Review.find({ product: review.product });
            const totalRating = reviews.reduce((sum, review) => sum + review.rating, 0);
            const averageRating = totalRating / reviews.length;
            await models_1.Product.findByIdAndUpdate(review.product, {
                rating: Math.round(averageRating * 10) / 10
            });
            (0, response_1.sendSuccess)(res, 'Review updated successfully', review);
        }
        catch (error) {
            console.error('Update review error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async deleteReview(req, res) {
        try {
            if (!req.user) {
                (0, response_1.sendError)(res, 'Authentication required', undefined, 401);
                return;
            }
            const { id } = req.params;
            const review = await models_1.Review.findOne({
                _id: id,
                user: req.user._id
            });
            if (!review) {
                (0, response_1.sendNotFound)(res, 'Review not found');
                return;
            }
            const productId = review.product;
            await models_1.Review.findByIdAndDelete(id);
            // Update product rating and review count
            const reviews = await models_1.Review.find({ product: productId });
            const averageRating = reviews.length > 0
                ? reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length
                : 0;
            await models_1.Product.findByIdAndUpdate(productId, {
                rating: Math.round(averageRating * 10) / 10,
                reviewCount: reviews.length
            });
            (0, response_1.sendSuccess)(res, 'Review deleted successfully');
        }
        catch (error) {
            console.error('Delete review error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    // New enhanced methods
    static async getReviews(req, res) {
        try {
            const { product, service, page, limit } = req.query;
            const result = await reviewService_1.ReviewService.getReviews({
                product: product,
                service: service,
                page: page ? parseInt(page) : undefined,
                limit: limit ? parseInt(limit) : undefined
            });
            (0, response_1.sendSuccess)(res, 'Reviews retrieved successfully', result);
        }
        catch (error) {
            console.error('Get reviews error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async getReviewStats(req, res) {
        try {
            const { product, service } = req.query;
            const stats = await reviewService_1.ReviewService.getReviewStats(product, service);
            (0, response_1.sendSuccess)(res, 'Review statistics retrieved successfully', stats);
        }
        catch (error) {
            console.error('Get review stats error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async getUserReviews(req, res) {
        try {
            if (!req.user) {
                (0, response_1.sendError)(res, 'Authentication required', undefined, 401);
                return;
            }
            const { page, limit } = req.query;
            const result = await reviewService_1.ReviewService.getUserReviews(req.user._id, page ? parseInt(page) : undefined, limit ? parseInt(limit) : undefined);
            (0, response_1.sendSuccess)(res, 'User reviews retrieved successfully', result);
        }
        catch (error) {
            console.error('Get user reviews error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    // Admin methods
    static async getAllReviews(req, res) {
        try {
            if (!req.user || req.user.role !== 'admin') {
                (0, response_1.sendError)(res, 'Admin access required', undefined, 403);
                return;
            }
            const { status, page, limit, search } = req.query;
            const result = await reviewService_1.ReviewService.getAllReviews({
                status: status,
                page: page ? parseInt(page) : undefined,
                limit: limit ? parseInt(limit) : undefined,
                search: search
            });
            (0, response_1.sendSuccess)(res, 'All reviews retrieved successfully', result);
        }
        catch (error) {
            console.error('Get all reviews error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async updateReviewStatus(req, res) {
        try {
            if (!req.user || req.user.role !== 'admin') {
                (0, response_1.sendError)(res, 'Admin access required', undefined, 403);
                return;
            }
            const { id } = req.params;
            const { status } = req.body;
            if (!['approved', 'rejected'].includes(status)) {
                (0, response_1.sendError)(res, 'Invalid status. Must be "approved" or "rejected"', undefined, 400);
                return;
            }
            const review = await reviewService_1.ReviewService.updateReviewStatus(id, status);
            if (!review) {
                (0, response_1.sendError)(res, 'Review not found', undefined, 404);
                return;
            }
            (0, response_1.sendSuccess)(res, `Review ${status} successfully`, review);
        }
        catch (error) {
            console.error('Update review status error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async adminDeleteReview(req, res) {
        try {
            if (!req.user || req.user.role !== 'admin') {
                (0, response_1.sendError)(res, 'Admin access required', undefined, 403);
                return;
            }
            const { id } = req.params;
            const deleted = await reviewService_1.ReviewService.deleteReview(id);
            if (!deleted) {
                (0, response_1.sendError)(res, 'Review not found', undefined, 404);
                return;
            }
            (0, response_1.sendSuccess)(res, 'Review deleted successfully');
        }
        catch (error) {
            console.error('Admin delete review error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
}
exports.ReviewController = ReviewController;
