"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const controllers_1 = require("../controllers");
const auth_1 = require("../middleware/auth");
const validation_1 = require("../middleware/validation");
const validation_2 = require("../utils/validation");
const router = (0, express_1.Router)();
// POST /api/gift-cards
router.post('/', auth_1.authenticate, controllers_1.GiftCardController.createGiftCard);
// GET /api/gift-cards/:code/balance
router.get('/:code/balance', controllers_1.GiftCardController.getGiftCardBalance);
// GET /api/gift-cards/user
router.get('/user', auth_1.authenticate, (0, validation_1.validate)(validation_2.paginationValidation), controllers_1.GiftCardController.getUserGiftCards);
// Admin routes
// GET /api/admin/gift-cards (admin only)
router.get('/admin/all', auth_1.authenticate, (0, auth_1.authorize)('admin'), (0, validation_1.validate)(validation_2.paginationValidation), controllers_1.GiftCardController.getAllGiftCards);
exports.default = router;
