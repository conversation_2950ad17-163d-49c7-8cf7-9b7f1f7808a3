"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.LoyaltyReward = exports.LoyaltyTransaction = exports.LoyaltyPoints = void 0;
const mongoose_1 = __importStar(require("mongoose"));
const loyaltyPointsSchema = new mongoose_1.Schema({
    user: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    points: {
        type: Number,
        default: 0,
        min: 0
    },
    totalEarned: {
        type: Number,
        default: 0,
        min: 0
    },
    totalRedeemed: {
        type: Number,
        default: 0,
        min: 0
    },
    tier: {
        type: String,
        enum: ['bronze', 'silver', 'gold', 'platinum'],
        default: 'bronze'
    }
}, {
    timestamps: true
});
const loyaltyTransactionSchema = new mongoose_1.Schema({
    user: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    type: {
        type: String,
        enum: ['earned', 'redeemed'],
        required: true
    },
    points: {
        type: Number,
        required: true
    },
    description: {
        type: String,
        required: true,
        trim: true
    },
    orderId: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'Order'
    },
    rewardId: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'LoyaltyReward'
    }
}, {
    timestamps: true
});
const loyaltyRewardSchema = new mongoose_1.Schema({
    name: {
        type: String,
        required: true,
        trim: true,
        maxlength: 100
    },
    description: {
        type: String,
        required: true,
        trim: true,
        maxlength: 500
    },
    pointsCost: {
        type: Number,
        required: true,
        min: 1
    },
    value: {
        type: Number,
        required: true,
        min: 0
    },
    type: {
        type: String,
        enum: ['discount', 'freeService', 'freeProduct', 'cashback'],
        required: true
    },
    isActive: {
        type: Boolean,
        default: true
    },
    expiryDays: {
        type: Number,
        default: 30,
        min: 1
    }
}, {
    timestamps: true
});
// Update tier based on total earned points
loyaltyPointsSchema.pre('save', function (next) {
    if (this.totalEarned >= 10000) {
        this.tier = 'platinum';
    }
    else if (this.totalEarned >= 5000) {
        this.tier = 'gold';
    }
    else if (this.totalEarned >= 1000) {
        this.tier = 'silver';
    }
    else {
        this.tier = 'bronze';
    }
    next();
});
// Index for better query performance
loyaltyPointsSchema.index({ user: 1 }, { unique: true });
loyaltyTransactionSchema.index({ user: 1 });
loyaltyTransactionSchema.index({ createdAt: -1 });
loyaltyRewardSchema.index({ isActive: 1 });
loyaltyRewardSchema.index({ pointsCost: 1 });
exports.LoyaltyPoints = mongoose_1.default.model('LoyaltyPoints', loyaltyPointsSchema);
exports.LoyaltyTransaction = mongoose_1.default.model('LoyaltyTransaction', loyaltyTransactionSchema);
exports.LoyaltyReward = mongoose_1.default.model('LoyaltyReward', loyaltyRewardSchema);
