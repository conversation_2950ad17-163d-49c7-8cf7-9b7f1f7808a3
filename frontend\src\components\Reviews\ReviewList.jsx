import { useState, useEffect } from 'react';
import { FiStar, FiUser, FiMoreVertical, FiEdit3, FiTrash2, FiCheck } from 'react-icons/fi';
import { useToast } from '../../contexts/ToastContext';

const ReviewList = ({ 
  reviews = [], 
  branding, 
  currentUser = null,
  onEditReview = null,
  onDeleteReview = null,
  showActions = false,
  loading = false 
}) => {
  const { showError, showSuccess } = useToast();
  const [expandedReviews, setExpandedReviews] = useState(new Set());
  const [actionMenuOpen, setActionMenuOpen] = useState(null);

  const toggleExpanded = (reviewId) => {
    const newExpanded = new Set(expandedReviews);
    if (newExpanded.has(reviewId)) {
      newExpanded.delete(reviewId);
    } else {
      newExpanded.add(reviewId);
    }
    setExpandedReviews(newExpanded);
  };

  const handleDeleteClick = async (review) => {
    if (window.confirm(branding?.reviews?.deleteConfirmMessage || 'Are you sure you want to delete this review?')) {
      try {
        await onDeleteReview(review._id);
        showSuccess('Review deleted successfully');
        setActionMenuOpen(null);
      } catch (error) {
        showError(`Failed to delete review: ${error.message}`);
      }
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const renderStars = (rating) => {
    return (
      <div className="flex items-center">
        {[1, 2, 3, 4, 5].map((star) => (
          <FiStar
            key={star}
            className={`w-4 h-4 ${
              star <= rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
            }`}
          />
        ))}
      </div>
    );
  };

  const getReviewerName = (review) => {
    if (review.user?.firstName && review.user?.lastName) {
      return `${review.user.firstName} ${review.user.lastName}`;
    } else if (review.user?.name) {
      return review.user.name;
    } else {
      return 'Anonymous User';
    }
  };

  const isOwnReview = (review) => {
    return currentUser && review.user?._id === currentUser._id;
  };

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, index) => (
          <div key={index} className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 animate-pulse">
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gray-300 rounded-full"></div>
                <div>
                  <div className="h-4 bg-gray-300 rounded w-24 mb-2"></div>
                  <div className="h-3 bg-gray-300 rounded w-16"></div>
                </div>
              </div>
              <div className="h-4 bg-gray-300 rounded w-20"></div>
            </div>
            <div className="h-4 bg-gray-300 rounded w-full mb-2"></div>
            <div className="h-4 bg-gray-300 rounded w-3/4"></div>
          </div>
        ))}
      </div>
    );
  }

  if (reviews.length === 0) {
    return (
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-white/20 text-center">
        <FiStar className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          {branding?.reviews?.noReviewsMessage || 'No reviews yet'}
        </h3>
        <p className="text-gray-600">
          Be the first to share your experience!
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {reviews.map((review) => {
        const isExpanded = expandedReviews.has(review._id);
        const shouldTruncate = review.comment.length > 200;
        const displayComment = isExpanded || !shouldTruncate 
          ? review.comment 
          : review.comment.substring(0, 200) + '...';

        return (
          <div key={review._id} className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 hover:shadow-xl transition-all duration-200">
            {/* Header */}
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                  <FiUser className="w-5 h-5 text-white" />
                </div>
                <div>
                  <div className="flex items-center space-x-2">
                    <h4 className="font-medium text-gray-900">
                      {getReviewerName(review)}
                    </h4>
                    {review.isVerifiedPurchase && (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        <FiCheck className="w-3 h-3 mr-1" />
                        {branding?.reviews?.verifiedPurchaseLabel || 'Verified Purchase'}
                      </span>
                    )}
                  </div>
                  <div className="flex items-center space-x-2 mt-1">
                    {renderStars(review.rating)}
                    <span className="text-sm text-gray-500">
                      {formatDate(review.createdAt)}
                    </span>
                  </div>
                </div>
              </div>

              {/* Actions Menu */}
              {showActions && isOwnReview(review) && (
                <div className="relative">
                  <button
                    onClick={() => setActionMenuOpen(actionMenuOpen === review._id ? null : review._id)}
                    className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors duration-200"
                  >
                    <FiMoreVertical className="w-4 h-4" />
                  </button>

                  {actionMenuOpen === review._id && (
                    <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-10">
                      {onEditReview && (
                        <button
                          onClick={() => {
                            onEditReview(review);
                            setActionMenuOpen(null);
                          }}
                          className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200"
                        >
                          <FiEdit3 className="w-4 h-4 mr-2" />
                          Edit Review
                        </button>
                      )}
                      {onDeleteReview && (
                        <button
                          onClick={() => handleDeleteClick(review)}
                          className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors duration-200"
                        >
                          <FiTrash2 className="w-4 h-4 mr-2" />
                          Delete Review
                        </button>
                      )}
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Review Content */}
            <div className="mb-4">
              <h5 className="font-medium text-gray-900 mb-2">{review.title}</h5>
              <p className="text-gray-700 leading-relaxed">
                {displayComment}
              </p>
              
              {shouldTruncate && (
                <button
                  onClick={() => toggleExpanded(review._id)}
                  className="mt-2 text-blue-600 hover:text-blue-800 text-sm font-medium transition-colors duration-200"
                >
                  {isExpanded 
                    ? (branding?.reviews?.showLessButton || 'Show Less')
                    : (branding?.reviews?.showMoreButton || 'Show More')
                  }
                </button>
              )}
            </div>

            {/* Footer */}
            <div className="flex items-center justify-between pt-4 border-t border-gray-200">
              <div className="flex items-center space-x-4">
                {review.helpfulVotes > 0 && (
                  <span className="text-sm text-gray-500">
                    {review.helpfulVotes} people found this helpful
                  </span>
                )}
              </div>
              
              <div className="flex items-center space-x-2">
                {review.status === 'pending' && isOwnReview(review) && (
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                    Pending Approval
                  </span>
                )}
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default ReviewList;
