"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const controllers_1 = require("../../controllers");
const auth_1 = require("../../middleware/auth");
const validation_1 = require("../../middleware/validation");
const validation_2 = require("../../utils/validation");
const router = (0, express_1.Router)();
// POST /api/v2/auth/register
router.post('/register', (0, validation_1.validate)(validation_2.registerValidation), controllers_1.AuthController.register);
// POST /api/v2/auth/login
router.post('/login', (0, validation_1.validate)(validation_2.loginValidation), controllers_1.AuthController.login);
// POST /api/v2/auth/check-email - Check if email exists and get user role
router.post('/check-email', controllers_1.AuthController.checkEmail);
// POST /api/v2/auth/login-with-email-phone - Login with email and phone (no password)
router.post('/login-with-email-phone', async (req, res) => {
    try {
        const { email, phone } = req.body;
        if (!email || !phone) {
            return res.status(400).json({
                success: false,
                message: 'Email and phone number are required'
            });
        }
        // Find user by email and phone
        const user = await require('../../models').User.findOne({
            email: email.toLowerCase(),
            phone: phone
        });
        if (!user) {
            return res.status(401).json({
                success: false,
                message: 'No account found with this email and phone combination'
            });
        }
        // Generate tokens (same as regular login)
        const { generateToken, generateRefreshToken } = require('../../utils/jwt');
        const payload = {
            userId: user._id,
            email: user.email,
            role: user.role
        };
        const token = generateToken(payload);
        const refreshToken = generateRefreshToken(payload);
        // Remove password from user object
        const userResponse = user.toObject();
        delete userResponse.password;
        res.json({
            success: true,
            message: 'Login successful',
            data: {
                user: userResponse,
                token,
                refreshToken
            }
        });
    }
    catch (error) {
        console.error('Email+Phone login error:', error);
        res.status(500).json({
            success: false,
            message: 'Login failed'
        });
    }
});
// POST /api/v2/auth/login-email-only - Login with email only for regular users
router.post('/login-email-only', async (req, res) => {
    try {
        const { email } = req.body;
        if (!email) {
            return res.status(400).json({
                success: false,
                message: 'Email is required'
            });
        }
        // Find user by email
        const user = await require('../../models').User.findOne({
            email: email.toLowerCase()
        });
        if (!user) {
            return res.status(401).json({
                success: false,
                message: 'No account found with this email address'
            });
        }
        // Check if user is admin - admins must use password login
        if (user.role === 'admin') {
            return res.status(401).json({
                success: false,
                message: 'Admin users must login with password'
            });
        }
        // Generate tokens (same as regular login)
        const { generateToken, generateRefreshToken } = require('../../utils/jwt');
        const payload = {
            userId: user._id,
            email: user.email,
            role: user.role
        };
        const token = generateToken(payload);
        const refreshToken = generateRefreshToken(payload);
        // Remove password from user object
        const userResponse = user.toObject();
        delete userResponse.password;
        res.json({
            success: true,
            message: 'Login successful',
            data: {
                user: userResponse,
                token,
                refreshToken
            }
        });
    }
    catch (error) {
        console.error('Email-only login error:', error);
        res.status(500).json({
            success: false,
            message: 'Login failed'
        });
    }
});
// POST /api/v2/auth/forgot-password
router.post('/forgot-password', (0, validation_1.validate)(validation_2.forgotPasswordValidation), controllers_1.AuthController.forgotPassword);
// POST /api/v2/auth/reset-password
router.post('/reset-password', controllers_1.AuthController.resetPassword);
// POST /api/v2/auth/check-email
router.post('/check-email', controllers_1.AuthController.checkEmail);
// GET /api/v2/auth/verify
router.get('/verify', auth_1.authenticate, controllers_1.AuthController.verify);
// POST /api/v2/auth/logout
router.post('/logout', auth_1.authenticate, controllers_1.AuthController.logout);
// GET /api/v2/auth/me - Get current user info (simplified for frontend v2)
router.get('/me', auth_1.authenticate, (req, res) => {
    const user = req.user;
    if (!user) {
        return res.status(401).json({
            success: false,
            message: 'User not authenticated'
        });
    }
    res.json({
        success: true,
        data: {
            id: user._id,
            email: user.email,
            firstName: user.firstName,
            lastName: user.lastName,
            phone: user.phone,
            role: user.role,
            createdAt: user.createdAt
        }
    });
});
exports.default = router;
