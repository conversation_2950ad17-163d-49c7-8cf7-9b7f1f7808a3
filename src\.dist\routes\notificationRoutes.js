"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const controllers_1 = require("../controllers");
const auth_1 = require("../middleware/auth");
const validation_1 = require("../middleware/validation");
const validation_2 = require("../utils/validation");
const router = (0, express_1.Router)();
// GET /api/notifications
router.get('/', auth_1.authenticate, (0, validation_1.validate)(validation_2.paginationValidation), controllers_1.NotificationController.getUserNotifications);
// PUT /api/notifications/:id/read
router.put('/:id/read', auth_1.authenticate, (0, validation_1.validate)((0, validation_2.mongoIdValidation)()), controllers_1.NotificationController.markAsRead);
// PUT /api/notifications/read-all
router.put('/read-all', auth_1.authenticate, controllers_1.NotificationController.markAllAsRead);
// DELETE /api/notifications/:id
router.delete('/:id', auth_1.authenticate, (0, validation_1.validate)((0, validation_2.mongoIdValidation)()), controllers_1.NotificationController.deleteNotification);
// Admin routes
// POST /api/notifications (admin only)
router.post('/', auth_1.authenticate, (0, auth_1.authorize)('admin'), controllers_1.NotificationController.createNotification);
exports.default = router;
