"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.Appointment = void 0;
const mongoose_1 = __importStar(require("mongoose"));
const appointmentSchema = new mongoose_1.Schema({
    user: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'User',
        required: false // Allow guest bookings without user account
    },
    service: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'Service',
        required: [true, 'Service is required']
    },
    date: {
        type: Date,
        required: [true, 'Appointment date is required'],
        validate: {
            validator: function (value) {
                // Only validate date for new appointments or when date is being modified
                // Allow past dates when just updating status (e.g., cancelling)
                if (this.isNew || this.isModified('date')) {
                    return value >= new Date();
                }
                return true;
            },
            message: 'Appointment date cannot be in the past'
        }
    },
    time: {
        type: String,
        required: [true, 'Appointment time is required']
    },
    status: {
        type: String,
        enum: ['pending', 'confirmed', 'completed', 'cancelled'],
        default: 'pending'
    },
    type: {
        type: String,
        enum: ['consultation', 'service'],
        default: 'consultation'
    },
    paymentStatus: {
        type: String,
        enum: ['pending', 'paid', 'refunded'],
        default: 'pending'
    },
    customerInfo: {
        name: {
            type: String,
            required: [true, 'Customer name is required'],
            trim: true,
            maxlength: [50, 'Name cannot be more than 50 characters']
        },
        email: {
            type: String,
            required: [true, 'Customer email is required'],
            lowercase: true,
            trim: true,
            match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
        },
        phone: {
            type: String,
            required: false, // Make phone optional for now
            trim: true,
            validate: {
                validator: function (v) {
                    // Allow empty/undefined phone numbers
                    if (!v)
                        return true;
                    // More flexible phone validation - allows various formats
                    return /^[\+]?[1-9][\d\s\-\(\)\.]{7,15}$/.test(v);
                },
                message: 'Please enter a valid phone number'
            }
        }
    },
    message: {
        type: String,
        trim: true,
        maxlength: [500, 'Message cannot be more than 500 characters']
    }
}, {
    timestamps: true
});
// Index for better query performance
appointmentSchema.index({ user: 1 });
appointmentSchema.index({ service: 1 });
appointmentSchema.index({ date: 1 });
appointmentSchema.index({ status: 1 });
appointmentSchema.index({ date: 1, time: 1 });
// Compound index to prevent double booking
appointmentSchema.index({
    date: 1,
    time: 1,
    status: 1
}, {
    unique: true,
    partialFilterExpression: {
        status: { $in: ['pending', 'confirmed'] }
    }
});
exports.Appointment = mongoose_1.default.model('Appointment', appointmentSchema);
