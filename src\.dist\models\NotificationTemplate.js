"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationTemplate = void 0;
const mongoose_1 = __importStar(require("mongoose"));
const notificationTemplateSchema = new mongoose_1.Schema({
    type: {
        type: String,
        required: true,
        enum: [
            'appointment-reminder',
            'appointment-confirmation',
            'appointment-cancelled',
            'order-status',
            'order-shipped',
            'order-delivered',
            'payment-received',
            'welcome',
            'password-reset',
            'promotion'
        ]
    },
    channel: {
        type: String,
        required: true,
        enum: ['email', 'sms', 'push']
    },
    subject: {
        type: String,
        trim: true,
        maxlength: 200,
        required: function () {
            return this.channel === 'email';
        }
    },
    content: {
        type: String,
        required: true
    },
    variables: [{
            type: String,
            trim: true
        }],
    isActive: {
        type: Boolean,
        default: true
    }
}, {
    timestamps: true
});
// Compound index for unique template per type and channel
notificationTemplateSchema.index({ type: 1, channel: 1 }, { unique: true });
notificationTemplateSchema.index({ isActive: 1 });
exports.NotificationTemplate = mongoose_1.default.model('NotificationTemplate', notificationTemplateSchema);
