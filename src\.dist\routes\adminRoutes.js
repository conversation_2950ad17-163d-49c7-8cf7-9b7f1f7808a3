"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const controllers_1 = require("../controllers");
const auth_1 = require("../middleware/auth");
const validation_1 = require("../middleware/validation");
const validation_2 = require("../utils/validation");
const router = (0, express_1.Router)();
// All admin routes require authentication and admin role
router.use(auth_1.authenticate);
router.use((0, auth_1.authorize)('admin'));
// GET /api/admin/dashboard
router.get('/dashboard', controllers_1.AdminController.getDashboardStats);
// GET /api/admin/appointments
router.get('/appointments', (0, validation_1.validate)(validation_2.paginationValidation), controllers_1.AdminController.getAppointments);
// POST /api/admin/appointments
router.post('/appointments', controllers_1.AdminController.createAppointment);
// PUT /api/admin/appointments/:id
router.put('/appointments/:id', (0, validation_1.validate)((0, validation_2.mongoIdValidation)()), controllers_1.AdminController.updateAppointment);
// DELETE /api/admin/appointments/:id
router.delete('/appointments/:id', (0, validation_1.validate)((0, validation_2.mongoIdValidation)()), controllers_1.AdminController.deleteAppointment);
// GET /api/admin/customers
router.get('/customers', (0, validation_1.validate)(validation_2.paginationValidation), controllers_1.AdminController.getCustomers);
// POST /api/admin/customers
router.post('/customers', controllers_1.AdminController.createCustomer);
// PUT /api/admin/customers/:id
router.put('/customers/:id', (0, validation_1.validate)((0, validation_2.mongoIdValidation)()), controllers_1.AdminController.updateCustomer);
// DELETE /api/admin/customers/:id
router.delete('/customers/:id', (0, validation_1.validate)((0, validation_2.mongoIdValidation)()), controllers_1.AdminController.deleteCustomer);
// GET /api/admin/orders
router.get('/orders', (0, validation_1.validate)(validation_2.paginationValidation), controllers_1.AdminController.getOrders);
// PUT /api/admin/orders/:id
router.put('/orders/:id', (0, validation_1.validate)((0, validation_2.mongoIdValidation)()), controllers_1.AdminController.updateOrder);
// DELETE /api/admin/orders/:id
router.delete('/orders/:id', (0, validation_1.validate)((0, validation_2.mongoIdValidation)()), controllers_1.AdminController.deleteOrder);
// GET /api/admin/products
router.get('/products', (0, validation_1.validate)(validation_2.paginationValidation), controllers_1.AdminController.getProducts);
// POST /api/admin/products
router.post('/products', controllers_1.ProductController.createProduct);
// PUT /api/admin/products/:id
router.put('/products/:id', (0, validation_1.validate)((0, validation_2.mongoIdValidation)()), controllers_1.ProductController.updateProduct);
// DELETE /api/admin/products/:id
router.delete('/products/:id', (0, validation_1.validate)((0, validation_2.mongoIdValidation)()), controllers_1.ProductController.deleteProduct);
// GET /api/admin/services
router.get('/services', (0, validation_1.validate)(validation_2.paginationValidation), controllers_1.AdminController.getServices);
// POST /api/admin/services
router.post('/services', controllers_1.ServiceController.createService);
// PUT /api/admin/services/:id
router.put('/services/:id', (0, validation_1.validate)((0, validation_2.mongoIdValidation)()), controllers_1.ServiceController.updateService);
// DELETE /api/admin/services/:id
router.delete('/services/:id', (0, validation_1.validate)((0, validation_2.mongoIdValidation)()), controllers_1.ServiceController.deleteService);
// GET /api/admin/branding
router.get('/branding', controllers_1.BrandingController.getBrandingContent);
// PUT /api/admin/branding
router.put('/branding', controllers_1.BrandingController.updateBrandingContent);
// PUT /api/admin/branding/:section
router.put('/branding/:section', controllers_1.BrandingController.updateBrandingSection);
exports.default = router;
