import { useState } from 'react';
import { type AdminAppointment } from '../../utils/api';
import Pagination from '../Pagination';

interface AppointmentListProps {
  appointments: AdminAppointment[];
  loading: boolean;
  selectedAppointments: string[];
  onSelectionChange: (selected: string[]) => void;
  onStatusUpdate: (id: string, status: string) => void;
  onEdit: (appointment: AdminAppointment) => void;
  onView: (appointment: AdminAppointment) => void;
  onDelete: (id: string) => void;
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
  onPageChange: (page: number) => void;
}

export default function AppointmentList({
  appointments,
  loading,
  selectedAppointments,
  onSelectionChange,
  onStatusUpdate,
  onEdit,
  onView,
  onDelete,
  pagination,
  onPageChange
}: AppointmentListProps) {
  const [sortField, setSortField] = useState<string>('date');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');

  // Handle select all
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      onSelectionChange(appointments.map(apt => apt.id));
    } else {
      onSelectionChange([]);
    }
  };

  // Handle individual selection
  const handleSelect = (appointmentId: string, checked: boolean) => {
    if (checked) {
      onSelectionChange([...selectedAppointments, appointmentId]);
    } else {
      onSelectionChange(selectedAppointments.filter(id => id !== appointmentId));
    }
  };

  // Get status badge class
  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'pending': return 'status-badge status-pending';
      case 'confirmed': return 'status-badge status-confirmed';
      case 'completed': return 'status-badge status-completed';
      case 'cancelled': return 'status-badge status-cancelled';
      default: return 'status-badge';
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Format time
  const formatTime = (timeString: string) => {
    return timeString;
  };

  // Handle sort
  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const isAllSelected = appointments.length > 0 && selectedAppointments.length === appointments.length;
  const isPartiallySelected = selectedAppointments.length > 0 && selectedAppointments.length < appointments.length;

  if (loading) {
    return (
      <div className="appointment-list-loading">
        <div className="loading-spinner"></div>
        <p>Loading appointments...</p>
      </div>
    );
  }

  if (appointments.length === 0) {
    return (
      <div className="appointment-list-empty">
        <div className="empty-state">
          <div className="empty-icon">📅</div>
          <h3>No appointments found</h3>
          <p>No appointments match your current filters.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="appointment-list">
      <div className="appointment-table-container">
        <table className="appointment-table">
          <thead>
            <tr>
              <th className="checkbox-column">
                <input
                  type="checkbox"
                  checked={isAllSelected}
                  ref={input => {
                    if (input) input.indeterminate = isPartiallySelected;
                  }}
                  onChange={(e) => handleSelectAll(e.target.checked)}
                />
              </th>
              <th 
                className={`sortable ${sortField === 'customerInfo.name' ? `sorted-${sortDirection}` : ''}`}
                onClick={() => handleSort('customerInfo.name')}
              >
                Customer
              </th>
              <th 
                className={`sortable ${sortField === 'service.name' ? `sorted-${sortDirection}` : ''}`}
                onClick={() => handleSort('service.name')}
              >
                Service
              </th>
              <th 
                className={`sortable ${sortField === 'date' ? `sorted-${sortDirection}` : ''}`}
                onClick={() => handleSort('date')}
              >
                Date & Time
              </th>
              <th 
                className={`sortable ${sortField === 'status' ? `sorted-${sortDirection}` : ''}`}
                onClick={() => handleSort('status')}
              >
                Status
              </th>
              <th>Price</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {appointments.map((appointment) => (
              <tr key={appointment.id} className="appointment-row">
                <td className="checkbox-column">
                  <input
                    type="checkbox"
                    checked={selectedAppointments.includes(appointment.id)}
                    onChange={(e) => handleSelect(appointment.id, e.target.checked)}
                  />
                </td>
                <td className="customer-column">
                  <div className="customer-info">
                    <div className="customer-name">
                      {appointment.customerInfo.name || 
                       (appointment.user ? appointment.user.name : 'Guest')}
                    </div>
                    <div className="customer-email">
                      {appointment.customerInfo.email || appointment.user?.email}
                    </div>
                    {(appointment.customerInfo.phone || appointment.user?.phone) && (
                      <div className="customer-phone">
                        {appointment.customerInfo.phone || appointment.user?.phone}
                      </div>
                    )}
                  </div>
                </td>
                <td className="service-column">
                  <div className="service-info">
                    <div className="service-name">{appointment.service?.name || 'Service Unavailable'}</div>
                    <div className="service-category">{appointment.service?.category || 'N/A'}</div>
                    <div className="service-duration">{appointment.service?.duration ? `${appointment.service.duration} min` : 'N/A'}</div>
                  </div>
                </td>
                <td className="datetime-column">
                  <div className="datetime-info">
                    <div className="date">{formatDate(appointment.date)}</div>
                    <div className="time">{formatTime(appointment.time)}</div>
                  </div>
                </td>
                <td className="status-column">
                  <select
                    className={getStatusBadgeClass(appointment.status)}
                    value={appointment.status}
                    onChange={(e) => onStatusUpdate(appointment.id, e.target.value)}
                  >
                    <option value="pending">Pending</option>
                    <option value="confirmed">Confirmed</option>
                    <option value="completed">Completed</option>
                    <option value="cancelled">Cancelled</option>
                  </select>
                </td>
                <td className="price-column">
                  <div className="price">${appointment.service?.price || '0'}</div>
                </td>
                <td className="actions-column">
                  <div className="action-buttons">
                    <button
                      className="btn btn-sm btn-outline"
                      onClick={() => onView(appointment)}
                      title="View Details"
                    >
                      👁️
                    </button>
                    <button
                      className="btn btn-sm btn-outline"
                      onClick={() => onEdit(appointment)}
                      title="Edit"
                    >
                      ✏️
                    </button>
                    <button
                      className="btn btn-sm btn-danger"
                      onClick={() => onDelete(appointment.id)}
                      title="Delete"
                    >
                      🗑️
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {pagination.pages > 1 && (
        <Pagination
          currentPage={pagination.page}
          totalPages={pagination.pages}
          totalItems={pagination.total}
          itemsPerPage={pagination.limit}
          onPageChange={onPageChange}
        />
      )}
    </div>
  );
}
