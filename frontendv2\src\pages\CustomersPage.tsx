import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import Header from '../components/Layout/Header'
import { type User } from '../utils/api'

interface CustomersPageProps {
  currentUser: User | null;
  onLogout: () => void;
}

interface Customer {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  totalAppointments: number;
  lastAppointment?: string;
  totalSpent: number;
  status: 'active' | 'inactive';
  createdAt: string;
}

export default function CustomersPage({ currentUser, onLogout }: CustomersPageProps) {
  const navigate = useNavigate();
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<'name' | 'email' | 'totalSpent' | 'lastAppointment'>('name');

  useEffect(() => {
    if (!currentUser || currentUser.role !== 'admin') {
      navigate('/');
      return;
    }
    
    fetchCustomers();
  }, [currentUser, navigate]);

  const fetchCustomers = async () => {
    try {
      setLoading(true);
      // TODO: Replace with actual API call
      // const response = await customerAPI.getCustomers();
      
      // Mock data for now
      const mockCustomers: Customer[] = [
        {
          id: '1',
          firstName: 'Jane',
          lastName: 'Doe',
          email: '<EMAIL>',
          phone: '(*************',
          totalAppointments: 5,
          lastAppointment: '2024-01-15',
          totalSpent: 850,
          status: 'active',
          createdAt: '2023-06-15'
        },
        {
          id: '2',
          firstName: 'John',
          lastName: 'Smith',
          email: '<EMAIL>',
          phone: '(*************',
          totalAppointments: 2,
          lastAppointment: '2024-01-10',
          totalSpent: 260,
          status: 'active',
          createdAt: '2023-12-01'
        },
        {
          id: '3',
          firstName: 'Sarah',
          lastName: 'Johnson',
          email: '<EMAIL>',
          totalAppointments: 8,
          lastAppointment: '2023-11-20',
          totalSpent: 1200,
          status: 'inactive',
          createdAt: '2023-03-10'
        }
      ];
      
      setCustomers(mockCustomers);
    } catch (error) {
      console.error('Error fetching customers:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCustomerView = (customerId: string) => {
    // TODO: Navigate to customer detail page
    console.log('View customer:', customerId);
  };

  const handleCustomerEdit = (customerId: string) => {
    // TODO: Navigate to customer edit page
    console.log('Edit customer:', customerId);
  };

  const filteredAndSortedCustomers = customers
    .filter(customer => 
      customer.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      customer.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      customer.email.toLowerCase().includes(searchTerm.toLowerCase())
    )
    .sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return `${a.firstName} ${a.lastName}`.localeCompare(`${b.firstName} ${b.lastName}`);
        case 'email':
          return a.email.localeCompare(b.email);
        case 'totalSpent':
          return b.totalSpent - a.totalSpent;
        case 'lastAppointment':
          return new Date(b.lastAppointment || 0).getTime() - new Date(a.lastAppointment || 0).getTime();
        default:
          return 0;
      }
    });

  if (loading) {
    return (
      <div className="app">
        <Header currentUser={currentUser} onLogout={onLogout} />
        <main className="main-content">
          <div className="loading-container">
            <p>Loading customers...</p>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="app">
      <Header currentUser={currentUser} onLogout={onLogout} />
      
      <main className="main-content">
        <div className="page-header">
          <h1>Customers</h1>
          <div className="header-actions">
            <input
              type="text"
              placeholder="Search customers..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="search-input"
            />
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as any)}
              className="sort-select"
            >
              <option value="name">Sort by Name</option>
              <option value="email">Sort by Email</option>
              <option value="totalSpent">Sort by Total Spent</option>
              <option value="lastAppointment">Sort by Last Appointment</option>
            </select>
          </div>
        </div>

        <div className="customers-stats">
          <div className="stat-card">
            <h3>Total Customers</h3>
            <p className="stat-number">{customers.length}</p>
          </div>
          <div className="stat-card">
            <h3>Active Customers</h3>
            <p className="stat-number">{customers.filter(c => c.status === 'active').length}</p>
          </div>
          <div className="stat-card">
            <h3>Total Revenue</h3>
            <p className="stat-number">${customers.reduce((sum, c) => sum + c.totalSpent, 0)}</p>
          </div>
          <div className="stat-card">
            <h3>Average Spent</h3>
            <p className="stat-number">
              ${Math.round(customers.reduce((sum, c) => sum + c.totalSpent, 0) / customers.length)}
            </p>
          </div>
        </div>

        <div className="customers-list">
          {filteredAndSortedCustomers.length === 0 ? (
            <div className="empty-state">
              <p>No customers found matching your search.</p>
            </div>
          ) : (
            <div className="customers-table">
              <div className="table-header">
                <div>Name</div>
                <div>Email</div>
                <div>Phone</div>
                <div>Appointments</div>
                <div>Total Spent</div>
                <div>Last Visit</div>
                <div>Status</div>
                <div>Actions</div>
              </div>
              
              {filteredAndSortedCustomers.map(customer => (
                <div key={customer.id} className="table-row">
                  <div>{customer.firstName} {customer.lastName}</div>
                  <div>{customer.email}</div>
                  <div>{customer.phone || 'N/A'}</div>
                  <div>{customer.totalAppointments}</div>
                  <div>${customer.totalSpent}</div>
                  <div>{customer.lastAppointment || 'Never'}</div>
                  <div>
                    <span className={`status-badge ${customer.status}`}>
                      {customer.status}
                    </span>
                  </div>
                  <div className="table-actions">
                    <button 
                      className="btn-secondary"
                      onClick={() => handleCustomerView(customer.id)}
                    >
                      View
                    </button>
                    <button 
                      className="btn-secondary"
                      onClick={() => handleCustomerEdit(customer.id)}
                    >
                      Edit
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </main>
    </div>
  )
}
