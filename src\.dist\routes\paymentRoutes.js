"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const controllers_1 = require("../controllers");
const auth_1 = require("../middleware/auth");
const router = (0, express_1.Router)();
// GET /api/admin/payment-settings (admin only)
router.get('/payment-settings', auth_1.authenticate, (0, auth_1.authorize)('admin'), controllers_1.PaymentController.getPaymentSettings);
// PUT /api/admin/payment-settings (admin only)
router.put('/payment-settings', auth_1.authenticate, (0, auth_1.authorize)('admin'), controllers_1.PaymentController.updatePaymentSettings);
exports.default = router;
