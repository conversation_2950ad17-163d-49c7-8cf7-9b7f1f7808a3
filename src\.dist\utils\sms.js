"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SMSService = void 0;
class SMSService {
    static isConfigured() {
        return !!(process.env.TWILIO_ACCOUNT_SID &&
            process.env.TWILIO_AUTH_TOKEN &&
            process.env.TWILIO_PHONE_NUMBER);
    }
    static async sendSMS(options) {
        try {
            if (!this.isConfigured()) {
                console.log(`SMS would be sent to ${options.to}: ${options.message}`);
                return {
                    success: true,
                    messageId: `mock_${Date.now()}`,
                    error: 'SMS service not configured - using mock mode'
                };
            }
            // Production Twilio implementation
            const twilio = require('twilio');
            const client = twilio(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN);
            const message = await client.messages.create({
                body: options.message,
                from: process.env.TWILIO_PHONE_NUMBER,
                to: options.to
            });
            console.log(`SMS sent successfully to ${options.to}. Message SID: ${message.sid}`);
            return {
                success: true,
                messageId: message.sid
            };
        }
        catch (error) {
            console.error('SMS sending error:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }
    static async sendAppointmentReminder(phone, serviceName, date, time, customMessage) {
        const message = customMessage ||
            `Appointment Reminder: ${serviceName} on ${date} at ${time}. Please arrive 10 minutes early. Reply STOP to opt out.`;
        return this.sendSMS({ to: phone, message });
    }
    static async sendOrderUpdate(phone, orderNumber, status, trackingNumber) {
        let message = `Order Update: Your order #${orderNumber} is now ${status}.`;
        if (trackingNumber) {
            message += ` Tracking: ${trackingNumber}`;
        }
        message += ' Reply STOP to opt out.';
        return this.sendSMS({ to: phone, message });
    }
    static async sendWelcomeMessage(phone, name) {
        const message = `Welcome to MicroLocs, ${name}! Thank you for joining us. We're excited to help you with your hair care journey. Reply STOP to opt out.`;
        return this.sendSMS({ to: phone, message });
    }
    static async sendPasswordResetCode(phone, code) {
        const message = `Your MicroLocs password reset code is: ${code}. This code expires in 10 minutes. Do not share this code with anyone.`;
        return this.sendSMS({ to: phone, message });
    }
    static async sendPromotionalMessage(phone, promotion) {
        const message = `${promotion} Reply STOP to opt out of promotional messages.`;
        return this.sendSMS({ to: phone, message });
    }
}
exports.SMSService = SMSService;
