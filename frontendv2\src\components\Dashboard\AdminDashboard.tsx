import { useState, useEffect } from 'react';
import {
  type User,
  type Appointment
} from '../../utils/auth';
import { adminAPI, getCurrentUser } from '../../utils/api';
import AppointmentManagement from '../Admin/AppointmentManagement';
// Analytics import removed - not currently used
import LoadingSpinner from '../LoadingSpinner';
import { useAdminRealTime } from '../../hooks/useRealTimeUpdates';

interface AdminDashboardProps {
  onLogout: () => void;
}

export default function AdminDashboard({ onLogout }: AdminDashboardProps) {
  const [user] = useState(getCurrentUser() || { id: '', firstName: 'Admin', lastName: 'User', email: '', phone: '', role: 'admin' as const, createdAt: '', password: '' });
  const [users, setUsers] = useState<User[]>([]);
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [activeTab, setActiveTab] = useState<'dashboard' | 'appointments' | 'customers' | 'services' | 'settings'>('dashboard');
  const [dashboardData, setDashboardData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  // Remove duplicate login logic since authentication is handled at route level

  useEffect(() => {
    loadData();
  }, []);

  // Set up real-time updates
  useAdminRealTime({
    enabled: true,
    onDashboardUpdate: (data) => {
      setDashboardData(data);
    },
    onAppointmentUpdate: () => {
      // Refresh dashboard data when appointments are updated
      loadData();
    },
    onError: (error) => {
      console.error('Real-time update error:', error);
    }
  });

  const loadData = async () => {
    try {
      setLoading(true);

      console.log('🔄 Loading dashboard data...');
      console.log('🔑 Auth token:', localStorage.getItem('authToken'));
      console.log('👤 Current user:', localStorage.getItem('currentUser'));

      // Load dashboard data from API only
      const response = await adminAPI.getDashboard();
      console.log('📊 Dashboard API response:', response);

      if (response.success) {
        console.log('✅ Dashboard data loaded successfully:', response.data);
        setDashboardData(response.data);
        // Set users and appointments from API response if available
        if (response.data.users) {
          console.log('👥 Users data:', response.data.users);
          setUsers(response.data.users);
        }
        if (response.data.appointments) {
          console.log('📅 Appointments data:', response.data.appointments);
          setAppointments(response.data.appointments);
        }
      } else {
        console.error('❌ Dashboard API failed:', response);
      }
    } catch (error) {
      console.error('💥 Error loading dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  // Login logic removed - handled at route level

  const handleLogout = () => {
    // Clear any local storage and call parent logout
    localStorage.removeItem('currentUser');
    localStorage.removeItem('authToken');
    localStorage.removeItem('refreshToken');
    onLogout();
  };

  // Note: Appointment management functions moved to AppointmentManagement component

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'short',
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed': return '#28a745';
      case 'pending': return '#ffc107';
      case 'completed': return '#6c757d';
      case 'cancelled': return '#dc3545';
      default: return '#6c757d';
    }
  };

  // Analytics calculations
  const totalRevenue = appointments
    .filter(apt => apt.status === 'completed')
    .reduce((sum, apt) => sum + apt.totalPrice, 0);

  // Note: pendingRevenue calculation removed as it's not used

  const todayAppointments = appointments.filter(apt => {
    const today = new Date().toDateString();
    return new Date(apt.date).toDateString() === today;
  });

  const upcomingAppointments = appointments.filter(apt => 
    new Date(apt.date) >= new Date() && apt.status !== 'cancelled'
  );

  // Authentication is now handled at route level

  if (!user || user.role !== 'admin') {
    return <div>Access denied. Admin privileges required.</div>;
  }

  return (
    <div className="dashboard-container admin">
      {/* Header */}
      <header className="dashboard-header">
        <div className="dashboard-header-content">
          <div className="dashboard-logo">
            <h1>dammyspicybeauty - Admin</h1>
          </div>
          <div className="dashboard-user-menu">
            <span className="welcome-text">Welcome, {user.firstName}!</span>
            <button className="logout-button" onClick={handleLogout}>
              Logout
            </button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="dashboard-main">
        <div className="dashboard-content">
          {/* Navigation Tabs */}
          <div className="dashboard-tabs">
            <button
              className={`tab-button ${activeTab === 'dashboard' ? 'active' : ''}`}
              onClick={() => setActiveTab('dashboard')}
            >
              📊 DASHBOARD
            </button>
            <button
              className={`tab-button ${activeTab === 'appointments' ? 'active' : ''}`}
              onClick={() => setActiveTab('appointments')}
            >
              📅 APPOINTMENTS
            </button>
            <button
              className={`tab-button ${activeTab === 'customers' ? 'active' : ''}`}
              onClick={() => setActiveTab('customers')}
            >
              👥 CUSTOMERS
            </button>
            <button
              className={`tab-button ${activeTab === 'services' ? 'active' : ''}`}
              onClick={() => setActiveTab('services')}
            >
              🛍️ SERVICES
            </button>
            <button
              className={`tab-button ${activeTab === 'settings' ? 'active' : ''}`}
              onClick={() => setActiveTab('settings')}
            >
              ⚙️ SETTINGS
            </button>
          </div>

          {/* Tab Content */}
          <div className="tab-content">
            {activeTab === 'dashboard' && (
              <div className="overview-section">
                {loading ? (
                  <LoadingSpinner message="Loading dashboard data..." />
                ) : (
                  <>
                    <div className="stats-grid admin-stats">
                      <div className="stat-card">
                        <h3>{dashboardData?.totalAppointments || appointments.length}</h3>
                        <p>Total Appointments</p>
                      </div>
                      <div className="stat-card">
                        <h3>{dashboardData?.todayAppointments || todayAppointments.length}</h3>
                        <p>Today's Appointments</p>
                      </div>
                      <div className="stat-card">
                        <h3>{dashboardData?.totalUsers || users.filter(u => u.role === 'user').length}</h3>
                        <p>Total Clients</p>
                      </div>
                      <div className="stat-card">
                        <h3>${(dashboardData?.totalRevenue || totalRevenue).toFixed(2)}</h3>
                        <p>Total Revenue</p>
                      </div>
                    </div>

                    <div className="recent-activity">
                      <h3>Recent Appointments</h3>
                      <div className="appointments-list compact">
                        {(dashboardData?.recentAppointments || upcomingAppointments).slice(0, 5).map((appointment: any) => (
                          <div key={appointment.id || appointment._id} className="appointment-card compact">
                            <div className="appointment-header">
                              <h4>
                                {appointment.customerInfo?.name ||
                                 `${appointment.customerInfo?.firstName || ''} ${appointment.customerInfo?.lastName || ''}`.trim() ||
                                 (appointment.user ? `${appointment.user.firstName} ${appointment.user.lastName}` : 'Guest')}
                              </h4>
                              <span
                                className="status-badge"
                                style={{ backgroundColor: getStatusColor(appointment.status) }}
                              >
                                {appointment.status.toUpperCase()}
                              </span>
                            </div>
                            <div className="appointment-details">
                              <p>{appointment.service?.name || appointment.serviceName}</p>
                              <p>{formatDate(appointment.date)} at {appointment.time}</p>
                              <p>${(appointment.service?.price || appointment.totalPrice || 0).toFixed(2)}</p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Appointment Status Overview */}
                    {dashboardData?.appointmentStats && (
                      <div className="status-overview">
                        <h3>Appointment Status Overview</h3>
                        <div className="status-grid">
                          {Object.entries(dashboardData.appointmentStats).map(([status, count]: [string, any]) => (
                            <div key={status} className="status-stat">
                              <div className="status-count">{count}</div>
                              <div className="status-label">{status.charAt(0).toUpperCase() + status.slice(1)}</div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </>
                )}
              </div>
            )}

            {activeTab === 'appointments' && (
              <AppointmentManagement onAppointmentUpdate={loadData} />
            )}

            {activeTab === 'customers' && (
              <div className="users-section">
                <h3>Client Management</h3>
                <div className="users-list">
                  {users.filter(u => u.role === 'user').map(user => {
                    const userAppointments = appointments.filter(apt => apt.userId === user.id);
                    const totalSpent = userAppointments.reduce((sum, apt) => sum + apt.totalPrice, 0);
                    
                    return (
                      <div key={user.id} className="user-card">
                        <div className="user-header">
                          <h4>{user.firstName} {user.lastName}</h4>
                          <span className="user-stats">{userAppointments.length} appointments</span>
                        </div>
                        <div className="user-details">
                          <p><strong>Email:</strong> {user.email}</p>
                          <p><strong>Phone:</strong> {user.phone}</p>
                          <p><strong>Member Since:</strong> {new Date(user.createdAt).toLocaleDateString()}</p>
                          <p><strong>Total Spent:</strong> ${totalSpent.toFixed(2)}</p>
                          <p><strong>Last Login:</strong> {user.lastLogin ? new Date(user.lastLogin).toLocaleDateString() : 'Never'}</p>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}

            {activeTab === 'services' && (
              <div className="services-section">
                <h3>Service Management</h3>
                <p>Service management functionality coming soon...</p>
              </div>
            )}

            {activeTab === 'settings' && (
              <div className="settings-section">
                <h3>Admin Settings</h3>
                <p>Settings functionality coming soon...</p>
              </div>
            )}
          </div>
        </div>
      </main>
    </div>
  );
}
