"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BusinessController = void 0;
const models_1 = require("../models");
const response_1 = require("../utils/response");
class BusinessController {
    static async getBusinessProfile(req, res) {
        try {
            let profile = await models_1.BusinessProfile.findOne();
            if (!profile) {
                // Create default profile if none exists
                profile = await models_1.BusinessProfile.create({
                    name: 'Business Owner',
                    email: '<EMAIL>',
                    role: 'Hair Specialist',
                    businessSince: '2020',
                    yearsExperience: 5,
                    businessHours: [
                        { day: 'Monday', open: '09:00', close: '18:00', isClosed: false },
                        { day: 'Tuesday', open: '09:00', close: '18:00', isClosed: false },
                        { day: 'Wednesday', open: '09:00', close: '18:00', isClosed: false },
                        { day: 'Thursday', open: '09:00', close: '18:00', isClosed: false },
                        { day: 'Friday', open: '09:00', close: '18:00', isClosed: false },
                        { day: 'Saturday', open: '10:00', close: '16:00', isClosed: false },
                        { day: 'Sunday', open: '', close: '', isClosed: true }
                    ]
                });
            }
            (0, response_1.sendSuccess)(res, 'Business profile retrieved successfully', profile);
        }
        catch (error) {
            console.error('Get business profile error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async updateBusinessProfile(req, res) {
        try {
            const updateData = req.body;
            let profile = await models_1.BusinessProfile.findOne();
            if (!profile) {
                profile = await models_1.BusinessProfile.create(updateData);
            }
            else {
                Object.assign(profile, updateData);
                await profile.save();
            }
            (0, response_1.sendSuccess)(res, 'Business profile updated successfully', profile);
        }
        catch (error) {
            console.error('Update business profile error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async getBusinessHours(req, res) {
        try {
            const profile = await models_1.BusinessProfile.findOne();
            if (!profile) {
                (0, response_1.sendError)(res, 'Business profile not found');
                return;
            }
            (0, response_1.sendSuccess)(res, 'Business hours retrieved successfully', profile.businessHours);
        }
        catch (error) {
            console.error('Get business hours error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async updateBusinessHours(req, res) {
        try {
            const businessHours = req.body;
            let profile = await models_1.BusinessProfile.findOne();
            if (!profile) {
                (0, response_1.sendError)(res, 'Business profile not found');
                return;
            }
            profile.businessHours = businessHours;
            await profile.save();
            (0, response_1.sendSuccess)(res, 'Business hours updated successfully', profile.businessHours);
        }
        catch (error) {
            console.error('Update business hours error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
}
exports.BusinessController = BusinessController;
