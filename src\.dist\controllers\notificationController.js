"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationController = void 0;
const models_1 = require("../models");
const response_1 = require("../utils/response");
class NotificationController {
    static async getUserNotifications(req, res) {
        try {
            if (!req.user) {
                (0, response_1.sendError)(res, 'Authentication required', undefined, 401);
                return;
            }
            const { page = 1, limit = 20, isRead } = req.query;
            const pageNum = Number(page);
            const limitNum = Number(limit);
            const skip = (pageNum - 1) * limitNum;
            const filter = { user: req.user._id };
            if (isRead !== undefined) {
                filter.isRead = isRead === 'true';
            }
            const [notifications, total, unreadCount] = await Promise.all([
                models_1.Notification.find(filter)
                    .sort({ createdAt: -1 })
                    .skip(skip)
                    .limit(limitNum),
                models_1.Notification.countDocuments(filter),
                models_1.Notification.countDocuments({ user: req.user._id, isRead: false })
            ]);
            const totalPages = Math.ceil(total / limitNum);
            (0, response_1.sendSuccess)(res, 'Notifications retrieved successfully', {
                notifications,
                unreadCount,
                pagination: {
                    currentPage: pageNum,
                    totalPages,
                    totalItems: total,
                    itemsPerPage: limitNum,
                    hasNextPage: pageNum < totalPages,
                    hasPrevPage: pageNum > 1
                }
            });
        }
        catch (error) {
            console.error('Get user notifications error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async markAsRead(req, res) {
        try {
            if (!req.user) {
                (0, response_1.sendError)(res, 'Authentication required', undefined, 401);
                return;
            }
            const { id } = req.params;
            const notification = await models_1.Notification.findOne({
                _id: id,
                user: req.user._id
            });
            if (!notification) {
                (0, response_1.sendNotFound)(res, 'Notification not found');
                return;
            }
            notification.isRead = true;
            await notification.save();
            (0, response_1.sendSuccess)(res, 'Notification marked as read', notification);
        }
        catch (error) {
            console.error('Mark notification as read error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async markAllAsRead(req, res) {
        try {
            if (!req.user) {
                (0, response_1.sendError)(res, 'Authentication required', undefined, 401);
                return;
            }
            await models_1.Notification.updateMany({ user: req.user._id, isRead: false }, { isRead: true });
            (0, response_1.sendSuccess)(res, 'All notifications marked as read');
        }
        catch (error) {
            console.error('Mark all notifications as read error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async deleteNotification(req, res) {
        try {
            if (!req.user) {
                (0, response_1.sendError)(res, 'Authentication required', undefined, 401);
                return;
            }
            const { id } = req.params;
            const notification = await models_1.Notification.findOne({
                _id: id,
                user: req.user._id
            });
            if (!notification) {
                (0, response_1.sendNotFound)(res, 'Notification not found');
                return;
            }
            await models_1.Notification.findByIdAndDelete(id);
            (0, response_1.sendSuccess)(res, 'Notification deleted successfully');
        }
        catch (error) {
            console.error('Delete notification error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async createNotification(req, res) {
        try {
            const { user, title, message, type } = req.body;
            const notification = await models_1.Notification.create({
                user,
                title,
                message,
                type
            });
            (0, response_1.sendSuccess)(res, 'Notification created successfully', notification, 201);
        }
        catch (error) {
            console.error('Create notification error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
}
exports.NotificationController = NotificationController;
