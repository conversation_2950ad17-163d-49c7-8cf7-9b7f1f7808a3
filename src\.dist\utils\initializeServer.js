"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.initializeServer = void 0;
const User_1 = require("../models/User");
const config_1 = require("../config");
/**
 * Initialize server with default admin user
 * Creates admin user if it doesn't exist
 */
const initializeServer = async () => {
    try {
        console.log('🔧 Initializing server...');
        // Check if admin user exists
        const adminExists = await User_1.User.findOne({ email: config_1.config.ADMIN.EMAIL });
        if (!adminExists) {
            console.log('👤 Creating default admin user...');
            // Split the email to get name parts
            const emailParts = config_1.config.ADMIN.EMAIL.split('@')[0];
            const nameParts = emailParts.split(/[._-]/);
            const firstName = nameParts[0] || 'Admin';
            const lastName = nameParts[1] || 'User';
            const fullName = `${firstName} ${lastName}`;
            // Create admin user
            await User_1.User.create({
                name: fullName,
                firstName: firstName.charAt(0).toUpperCase() + firstName.slice(1),
                lastName: lastName.charAt(0).toUpperCase() + lastName.slice(1),
                email: config_1.config.ADMIN.EMAIL,
                phone: '+1234567890',
                password: config_1.config.ADMIN.PASSWORD,
                role: 'admin',
                isVerified: true
            });
            console.log(`✅ Default admin user created: ${config_1.config.ADMIN.EMAIL}`);
        }
        else {
            console.log(`✅ Admin user already exists: ${config_1.config.ADMIN.EMAIL}`);
        }
        console.log('🚀 Server initialization completed');
    }
    catch (error) {
        console.error('❌ Error during server initialization:', error);
        throw error;
    }
};
exports.initializeServer = initializeServer;
