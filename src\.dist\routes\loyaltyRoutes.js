"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const controllers_1 = require("../controllers");
const auth_1 = require("../middleware/auth");
const validation_1 = require("../middleware/validation");
const validation_2 = require("../utils/validation");
const router = (0, express_1.Router)();
// GET /api/users/loyalty
router.get('/', auth_1.authenticate, controllers_1.LoyaltyController.getUserLoyalty);
// POST /api/users/loyalty/redeem
router.post('/redeem', auth_1.authenticate, controllers_1.LoyaltyController.redeemReward);
// GET /api/users/loyalty/transactions
router.get('/transactions', auth_1.authenticate, (0, validation_1.validate)(validation_2.paginationValidation), controllers_1.LoyaltyController.getLoyaltyTransactions);
// GET /api/loyalty/rewards
router.get('/rewards', controllers_1.LoyaltyController.getLoyaltyRewards);
// Admin routes
// POST /api/admin/loyalty/rewards (admin only)
router.post('/admin/rewards', auth_1.authenticate, (0, auth_1.authorize)('admin'), controllers_1.LoyaltyController.createLoyaltyReward);
// PUT /api/admin/loyalty/rewards/:id (admin only)
router.put('/admin/rewards/:id', auth_1.authenticate, (0, auth_1.authorize)('admin'), (0, validation_1.validate)((0, validation_2.mongoIdValidation)()), controllers_1.LoyaltyController.updateLoyaltyReward);
// DELETE /api/admin/loyalty/rewards/:id (admin only)
router.delete('/admin/rewards/:id', auth_1.authenticate, (0, auth_1.authorize)('admin'), (0, validation_1.validate)((0, validation_2.mongoIdValidation)()), controllers_1.LoyaltyController.deleteLoyaltyReward);
exports.default = router;
