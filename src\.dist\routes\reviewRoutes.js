"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const controllers_1 = require("../controllers");
const auth_1 = require("../middleware/auth");
const validation_1 = require("../middleware/validation");
const validation_2 = require("../utils/validation");
const router = (0, express_1.Router)();
// Public routes
// GET /api/reviews - Get reviews for product or service
router.get('/', (0, validation_1.validate)(validation_2.paginationValidation), controllers_1.ReviewController.getReviews);
// GET /api/reviews/stats - Get review statistics
router.get('/stats', controllers_1.ReviewController.getReviewStats);
// GET /api/products/:id/reviews (legacy support)
router.get('/:id/reviews', (0, validation_1.validate)([...(0, validation_2.mongoIdValidation)(), ...validation_2.paginationValidation]), controllers_1.ReviewController.getProductReviews);
// User routes (authenticated)
// POST /api/reviews - Create new review
router.post('/', auth_1.authenticate, (0, validation_1.validate)(validation_2.createReviewValidation), controllers_1.ReviewController.createReview);
// GET /api/reviews/my - Get user's own reviews
router.get('/my', auth_1.authenticate, (0, validation_1.validate)(validation_2.paginationValidation), controllers_1.ReviewController.getUserReviews);
// PUT /api/reviews/:id - Update user's own review
router.put('/:id', auth_1.authenticate, (0, validation_1.validate)([...(0, validation_2.mongoIdValidation)(), ...validation_2.updateReviewValidation]), controllers_1.ReviewController.updateReview);
// DELETE /api/reviews/:id - Delete user's own review
router.delete('/:id', auth_1.authenticate, (0, validation_1.validate)((0, validation_2.mongoIdValidation)()), controllers_1.ReviewController.deleteReview);
// Admin routes
// GET /api/reviews/admin/all - Get all reviews for admin
router.get('/admin/all', auth_1.authenticate, (0, validation_1.validate)(validation_2.paginationValidation), controllers_1.ReviewController.getAllReviews);
// PUT /api/reviews/admin/:id/status - Update review status
router.put('/admin/:id/status', auth_1.authenticate, (0, validation_1.validate)([...(0, validation_2.mongoIdValidation)(), ...validation_2.reviewStatusValidation]), controllers_1.ReviewController.updateReviewStatus);
// DELETE /api/reviews/admin/:id - Admin delete any review
router.delete('/admin/:id', auth_1.authenticate, (0, validation_1.validate)((0, validation_2.mongoIdValidation)()), controllers_1.ReviewController.adminDeleteReview);
exports.default = router;
