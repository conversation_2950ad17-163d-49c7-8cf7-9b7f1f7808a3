"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ServiceController = void 0;
const models_1 = require("../models");
const response_1 = require("../utils/response");
const cloudinaryService_1 = require("../services/cloudinaryService");
class ServiceController {
    static async getAllServices(req, res) {
        try {
            const { category, active } = req.query;
            const filter = {};
            if (category) {
                filter.category = category;
            }
            if (active !== undefined) {
                filter.isActive = active === 'true';
            }
            else {
                filter.isActive = true; // Default to active services only
            }
            const services = await models_1.Service.find(filter).sort({ name: 1 });
            (0, response_1.sendSuccess)(res, 'Services retrieved successfully', services);
        }
        catch (error) {
            console.error('Get all services error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async getServiceById(req, res) {
        try {
            const { id } = req.params;
            const service = await models_1.Service.findById(id);
            if (!service) {
                (0, response_1.sendNotFound)(res, 'Service not found');
                return;
            }
            (0, response_1.sendSuccess)(res, 'Service retrieved successfully', service);
        }
        catch (error) {
            console.error('Get service by ID error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async getServiceCategories(req, res) {
        try {
            const categories = await models_1.Service.distinct('category', { isActive: true });
            (0, response_1.sendSuccess)(res, 'Service categories retrieved successfully', categories);
        }
        catch (error) {
            console.error('Get service categories error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async createService(req, res) {
        try {
            const serviceData = req.body;
            const service = await models_1.Service.create(serviceData);
            (0, response_1.sendSuccess)(res, 'Service created successfully', service, 201);
        }
        catch (error) {
            console.error('Create service error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async updateService(req, res) {
        try {
            const { id } = req.params;
            const updateData = req.body;
            const service = await models_1.Service.findByIdAndUpdate(id, updateData, { new: true, runValidators: true });
            if (!service) {
                (0, response_1.sendNotFound)(res, 'Service not found');
                return;
            }
            (0, response_1.sendSuccess)(res, 'Service updated successfully', service);
        }
        catch (error) {
            console.error('Update service error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async deleteService(req, res) {
        try {
            const { id } = req.params;
            const service = await models_1.Service.findByIdAndDelete(id);
            if (!service) {
                (0, response_1.sendNotFound)(res, 'Service not found');
                return;
            }
            (0, response_1.sendSuccess)(res, 'Service deleted successfully');
        }
        catch (error) {
            console.error('Delete service error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async uploadServiceImage(req, res) {
        try {
            const { id } = req.params;
            if (!req.file) {
                (0, response_1.sendError)(res, 'No image file provided', undefined, 400);
                return;
            }
            // Find the service
            const service = await models_1.Service.findById(id);
            if (!service) {
                (0, response_1.sendNotFound)(res, 'Service not found');
                return;
            }
            // Upload to Cloudinary using the unified uploader
            const imageUrl = await (0, cloudinaryService_1.uploadSingleFile)(req, 'serviceImage');
            if (!imageUrl) {
                (0, response_1.sendError)(res, 'Failed to upload image', undefined, 500);
                return;
            }
            // Update service with new image
            const updatedImages = service.images || [];
            updatedImages.push(imageUrl);
            service.images = updatedImages;
            // Set the first image as the main image if not set
            if (!service.image) {
                service.image = imageUrl;
            }
            await service.save();
            (0, response_1.sendSuccess)(res, 'Service image uploaded successfully', {
                imageUrl: imageUrl,
                service: service
            });
        }
        catch (error) {
            console.error('Upload service image error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async deleteServiceImage(req, res) {
        try {
            const { id } = req.params;
            const { imageUrl } = req.body;
            if (!imageUrl) {
                (0, response_1.sendError)(res, 'Image URL is required', undefined, 400);
                return;
            }
            // Find the service
            const service = await models_1.Service.findById(id);
            if (!service) {
                (0, response_1.sendNotFound)(res, 'Service not found');
                return;
            }
            // Remove image from service
            if (service.images) {
                service.images = service.images.filter(img => img !== imageUrl);
            }
            // If this was the main image, set a new main image
            if (service.image === imageUrl) {
                service.image = service.images && service.images.length > 0 ? service.images[0] : '';
            }
            await service.save();
            (0, response_1.sendSuccess)(res, 'Service image deleted successfully', service);
        }
        catch (error) {
            console.error('Delete service image error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
}
exports.ServiceController = ServiceController;
