"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ThemeController = void 0;
const models_1 = require("../models");
const response_1 = require("../utils/response");
class ThemeController {
    static async getThemeSettings(req, res) {
        try {
            let theme = await models_1.ThemeSettings.findOne({ isActive: true });
            if (!theme) {
                // Create default theme if none exists
                theme = await models_1.ThemeSettings.create({});
            }
            (0, response_1.sendSuccess)(res, 'Theme settings retrieved successfully', theme);
        }
        catch (error) {
            console.error('Get theme settings error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async updateThemeSettings(req, res) {
        try {
            const updateData = req.body;
            let theme = await models_1.ThemeSettings.findOne({ isActive: true });
            if (!theme) {
                theme = await models_1.ThemeSettings.create(updateData);
            }
            else {
                Object.assign(theme, updateData);
                await theme.save();
            }
            (0, response_1.sendSuccess)(res, 'Theme settings updated successfully', theme);
        }
        catch (error) {
            console.error('Update theme settings error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async getPublicTheme(req, res) {
        try {
            const theme = await models_1.ThemeSettings.findOne({ isActive: true });
            if (!theme) {
                // Return default theme
                (0, response_1.sendSuccess)(res, 'Public theme retrieved successfully', {
                    primaryColor: '#007bff',
                    secondaryColor: '#6c757d',
                    accentColor: '#28a745',
                    backgroundColor: '#ffffff',
                    textColor: '#333333',
                    fontFamily: 'Inter, sans-serif',
                    fontSize: '16px',
                    buttonStyle: 'rounded',
                    cardStyle: 'elevated',
                    borderRadius: '8px',
                    spacing: 'normal'
                });
                return;
            }
            // Return theme without sensitive data
            const publicTheme = {
                primaryColor: theme.primaryColor,
                secondaryColor: theme.secondaryColor,
                accentColor: theme.accentColor,
                backgroundColor: theme.backgroundColor,
                textColor: theme.textColor,
                fontFamily: theme.fontFamily,
                fontSize: theme.fontSize,
                buttonStyle: theme.buttonStyle,
                cardStyle: theme.cardStyle,
                borderRadius: theme.borderRadius,
                spacing: theme.spacing
            };
            (0, response_1.sendSuccess)(res, 'Public theme retrieved successfully', publicTheme);
        }
        catch (error) {
            console.error('Get public theme error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async resetTheme(req, res) {
        try {
            // Deactivate current theme
            await models_1.ThemeSettings.updateMany({}, { isActive: false });
            // Create new default theme
            const defaultTheme = await models_1.ThemeSettings.create({});
            (0, response_1.sendSuccess)(res, 'Theme reset to default successfully', defaultTheme);
        }
        catch (error) {
            console.error('Reset theme error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
}
exports.ThemeController = ThemeController;
