import { useToast } from '../contexts/ToastContext'

/**
 * Higher-order function that wraps API calls with automatic toast notifications
 * @param {Function} apiFunction - The API function to wrap
 * @param {Object} options - Configuration options
 * @param {string} options.loadingMessage - Message to show while loading
 * @param {string} options.successMessage - Message to show on success
 * @param {string} options.errorMessage - Message to show on error
 * @param {boolean} options.showLoading - Whether to show loading toast
 * @param {boolean} options.showSuccess - Whether to show success toast
 * @param {boolean} options.showError - Whether to show error toast
 * @param {Function} options.onSuccess - Callback on success
 * @param {Function} options.onError - Callback on error
 * @returns {Function} Wrapped API function
 */
export const withToast = (apiFunction, options = {}) => {
  const {
    loadingMessage = 'Processing...',
    successMessage = 'Operation completed successfully',
    errorMessage = 'Operation failed',
    showLoading = true,
    showSuccess = true,
    showError = true,
    onSuccess,
    onError,
    silent = false // Option to disable all toasts
  } = options

  return async (...args) => {
    if (silent) {
      // Execute without any toast notifications
      try {
        const result = await apiFunction(...args)
        if (onSuccess) onSuccess(result)
        return result
      } catch (error) {
        if (onError) onError(error)
        throw error
      }
    }

    // This function needs to be called within a component that has access to toast context
    // We'll return a function that accepts the toast methods
    return async (toastMethods) => {
      const { showLoading: showLoadingToast, showSuccess: showSuccessToast, showError: showErrorToast } = toastMethods

      let loadingToastId = null

      try {
        // Show loading toast
        if (showLoading) {
          loadingToastId = showLoadingToast(loadingMessage)
        }

        // Execute the API call
        const result = await apiFunction(...args)

        // Dismiss loading toast
        if (loadingToastId) {
          toastMethods.dismiss(loadingToastId)
        }

        // Show success toast
        if (showSuccess) {
          showSuccessToast(successMessage)
        }

        // Execute success callback
        if (onSuccess) {
          onSuccess(result)
        }

        return result
      } catch (error) {
        // Dismiss loading toast
        if (loadingToastId) {
          toastMethods.dismiss(loadingToastId)
        }

        // Show error toast
        if (showError) {
          const message = error.response?.data?.message || error.message || errorMessage
          showErrorToast(message)
        }

        // Execute error callback
        if (onError) {
          onError(error)
        }

        throw error
      }
    }
  }
}

/**
 * Hook that provides API functions with automatic toast notifications
 * @returns {Object} Object containing wrapped API functions and toast utilities
 */
export const useApiWithToast = () => {
  const toast = useToast()

  /**
   * Execute an API call with automatic toast notifications
   * @param {Function} apiFunction - The API function to execute
   * @param {Object} options - Configuration options
   * @returns {Promise} Promise that resolves with the API result
   */
  const executeWithToast = async (apiFunction, options = {}) => {
    const {
      loadingMessage = 'Processing...',
      successMessage = 'Operation completed successfully',
      errorMessage = 'Operation failed',
      showLoading = true,
      showSuccess = true,
      showError = true,
      onSuccess,
      onError,
      silent = false
    } = options

    if (silent) {
      try {
        const result = await apiFunction()
        if (onSuccess) onSuccess(result)
        return result
      } catch (error) {
        if (onError) onError(error)
        throw error
      }
    }

    let loadingToastId = null

    try {
      // Show loading toast
      if (showLoading) {
        loadingToastId = toast.showLoading(loadingMessage)
      }

      // Execute the API call
      const result = await apiFunction()

      // Dismiss loading toast
      if (loadingToastId) {
        toast.dismiss(loadingToastId)
      }

      // Show success toast
      if (showSuccess) {
        toast.showSuccess(successMessage)
      }

      // Execute success callback
      if (onSuccess) {
        onSuccess(result)
      }

      return result
    } catch (error) {
      // Dismiss loading toast
      if (loadingToastId) {
        toast.dismiss(loadingToastId)
      }

      // Show error toast
      if (showError) {
        const message = error.response?.data?.message || error.message || errorMessage
        toast.showError(message)
      }

      // Execute error callback
      if (onError) {
        onError(error)
      }

      throw error
    }
  }

  /**
   * Execute an API call using toast.promise for automatic state management
   * @param {Function} apiFunction - The API function to execute
   * @param {Object} messages - Messages for different states
   * @param {Object} options - Additional options
   * @returns {Promise} Promise that resolves with the API result
   */
  const executeWithPromiseToast = (apiFunction, messages = {}, options = {}) => {
    const {
      loading = 'Processing...',
      success = 'Operation completed successfully',
      error = 'Operation failed'
    } = messages

    return toast.promise(
      apiFunction(),
      {
        loading,
        success,
        error: (err) => err.response?.data?.message || err.message || error
      },
      options
    )
  }

  return {
    executeWithToast,
    executeWithPromiseToast,
    toast,
    // Convenience methods
    showSuccess: toast.showSuccess,
    showError: toast.showError,
    showWarning: toast.showWarning,
    showInfo: toast.showInfo,
    showLoading: toast.showLoading,
    dismiss: toast.dismiss,
    dismissAll: toast.dismissAll
  }
}

/**
 * Common toast messages for different operations
 */
export const TOAST_MESSAGES = {
  // Authentication
  LOGIN_SUCCESS: 'Welcome back!',
  LOGIN_ERROR: 'Login failed. Please check your credentials.',
  LOGOUT_SUCCESS: 'Logged out successfully',
  SIGNUP_SUCCESS: 'Account created successfully!',
  SIGNUP_ERROR: 'Failed to create account',
  
  // CRUD Operations
  CREATE_SUCCESS: 'Created successfully',
  CREATE_ERROR: 'Failed to create',
  UPDATE_SUCCESS: 'Updated successfully',
  UPDATE_ERROR: 'Failed to update',
  DELETE_SUCCESS: 'Deleted successfully',
  DELETE_ERROR: 'Failed to delete',
  
  // Data Loading
  LOADING: 'Loading...',
  LOAD_ERROR: 'Failed to load data',
  
  // Form Operations
  SAVE_SUCCESS: 'Changes saved successfully',
  SAVE_ERROR: 'Failed to save changes',
  
  // File Operations
  UPLOAD_SUCCESS: 'File uploaded successfully',
  UPLOAD_ERROR: 'Failed to upload file',
  
  // Appointments
  APPOINTMENT_CREATED: 'Appointment scheduled successfully',
  APPOINTMENT_UPDATED: 'Appointment updated successfully',
  APPOINTMENT_CANCELLED: 'Appointment cancelled successfully',
  
  // Orders
  ORDER_PLACED: 'Order placed successfully',
  ORDER_UPDATED: 'Order updated successfully',
  
  // Profile
  PROFILE_UPDATED: 'Profile updated successfully',
  PASSWORD_CHANGED: 'Password changed successfully',
  
  // Generic
  SUCCESS: 'Operation completed successfully',
  ERROR: 'Something went wrong',
  NETWORK_ERROR: 'Network error. Please check your connection.',
  UNAUTHORIZED: 'You are not authorized to perform this action',
  FORBIDDEN: 'Access denied',
  NOT_FOUND: 'Resource not found'
}

export default useApiWithToast
