"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LoyaltyController = void 0;
const models_1 = require("../models");
const response_1 = require("../utils/response");
class LoyaltyController {
    static async getUserLoyalty(req, res) {
        try {
            if (!req.user) {
                (0, response_1.sendError)(res, 'Authentication required', undefined, 401);
                return;
            }
            let loyaltyPoints = await models_1.LoyaltyPoints.findOne({ user: req.user._id });
            if (!loyaltyPoints) {
                loyaltyPoints = await models_1.LoyaltyPoints.create({ user: req.user._id });
            }
            // Get recent transactions
            const recentTransactions = await models_1.LoyaltyTransaction.find({ user: req.user._id })
                .sort({ createdAt: -1 })
                .limit(10);
            // Get available rewards
            const availableRewards = await models_1.LoyaltyReward.find({
                isActive: true,
                pointsCost: { $lte: loyaltyPoints.points }
            }).sort({ pointsCost: 1 });
            (0, response_1.sendSuccess)(res, 'User loyalty data retrieved successfully', {
                points: loyaltyPoints.points,
                totalEarned: loyaltyPoints.totalEarned,
                totalRedeemed: loyaltyPoints.totalRedeemed,
                tier: loyaltyPoints.tier,
                recentTransactions,
                availableRewards
            });
        }
        catch (error) {
            console.error('Get user loyalty error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async redeemReward(req, res) {
        try {
            if (!req.user) {
                (0, response_1.sendError)(res, 'Authentication required', undefined, 401);
                return;
            }
            const { rewardId } = req.body;
            const reward = await models_1.LoyaltyReward.findById(rewardId);
            if (!reward || !reward.isActive) {
                (0, response_1.sendNotFound)(res, 'Reward not found or inactive');
                return;
            }
            const loyaltyPoints = await models_1.LoyaltyPoints.findOne({ user: req.user._id });
            if (!loyaltyPoints) {
                (0, response_1.sendError)(res, 'Loyalty account not found');
                return;
            }
            if (loyaltyPoints.points < reward.pointsCost) {
                (0, response_1.sendError)(res, 'Insufficient points to redeem this reward');
                return;
            }
            // Deduct points
            loyaltyPoints.points -= reward.pointsCost;
            loyaltyPoints.totalRedeemed += reward.pointsCost;
            await loyaltyPoints.save();
            // Record transaction
            await models_1.LoyaltyTransaction.create({
                user: req.user._id,
                type: 'redeemed',
                points: -reward.pointsCost,
                description: `Redeemed: ${reward.name}`,
                rewardId: reward._id
            });
            (0, response_1.sendSuccess)(res, 'Reward redeemed successfully', {
                reward,
                remainingPoints: loyaltyPoints.points,
                tier: loyaltyPoints.tier
            });
        }
        catch (error) {
            console.error('Redeem reward error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async addPoints(userId, points, description, orderId) {
        try {
            let loyaltyPoints = await models_1.LoyaltyPoints.findOne({ user: userId });
            if (!loyaltyPoints) {
                loyaltyPoints = await models_1.LoyaltyPoints.create({ user: userId });
            }
            loyaltyPoints.points += points;
            loyaltyPoints.totalEarned += points;
            await loyaltyPoints.save();
            // Record transaction
            await models_1.LoyaltyTransaction.create({
                user: userId,
                type: 'earned',
                points,
                description,
                orderId
            });
        }
        catch (error) {
            console.error('Add points error:', error);
        }
    }
    static async getLoyaltyRewards(req, res) {
        try {
            const { active } = req.query;
            const filter = {};
            if (active !== undefined) {
                filter.isActive = active === 'true';
            }
            const rewards = await models_1.LoyaltyReward.find(filter).sort({ pointsCost: 1 });
            (0, response_1.sendSuccess)(res, 'Loyalty rewards retrieved successfully', rewards);
        }
        catch (error) {
            console.error('Get loyalty rewards error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async createLoyaltyReward(req, res) {
        try {
            const rewardData = req.body;
            const reward = await models_1.LoyaltyReward.create(rewardData);
            (0, response_1.sendSuccess)(res, 'Loyalty reward created successfully', reward, 201);
        }
        catch (error) {
            console.error('Create loyalty reward error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async updateLoyaltyReward(req, res) {
        try {
            const { id } = req.params;
            const updateData = req.body;
            const reward = await models_1.LoyaltyReward.findByIdAndUpdate(id, updateData, { new: true, runValidators: true });
            if (!reward) {
                (0, response_1.sendNotFound)(res, 'Loyalty reward not found');
                return;
            }
            (0, response_1.sendSuccess)(res, 'Loyalty reward updated successfully', reward);
        }
        catch (error) {
            console.error('Update loyalty reward error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async deleteLoyaltyReward(req, res) {
        try {
            const { id } = req.params;
            const reward = await models_1.LoyaltyReward.findByIdAndDelete(id);
            if (!reward) {
                (0, response_1.sendNotFound)(res, 'Loyalty reward not found');
                return;
            }
            (0, response_1.sendSuccess)(res, 'Loyalty reward deleted successfully');
        }
        catch (error) {
            console.error('Delete loyalty reward error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async getLoyaltyTransactions(req, res) {
        try {
            if (!req.user) {
                (0, response_1.sendError)(res, 'Authentication required', undefined, 401);
                return;
            }
            const { page = 1, limit = 20, type } = req.query;
            const filter = { user: req.user._id };
            if (type) {
                filter.type = type;
            }
            const pageNum = Number(page);
            const limitNum = Number(limit);
            const skip = (pageNum - 1) * limitNum;
            const [transactions, total] = await Promise.all([
                models_1.LoyaltyTransaction.find(filter)
                    .populate('orderId', 'orderNumber totalAmount')
                    .populate('rewardId', 'name type')
                    .sort({ createdAt: -1 })
                    .skip(skip)
                    .limit(limitNum),
                models_1.LoyaltyTransaction.countDocuments(filter)
            ]);
            const totalPages = Math.ceil(total / limitNum);
            (0, response_1.sendSuccess)(res, 'Loyalty transactions retrieved successfully', {
                transactions,
                pagination: {
                    currentPage: pageNum,
                    totalPages,
                    totalItems: total,
                    itemsPerPage: limitNum,
                    hasNextPage: pageNum < totalPages,
                    hasPrevPage: pageNum > 1
                }
            });
        }
        catch (error) {
            console.error('Get loyalty transactions error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
}
exports.LoyaltyController = LoyaltyController;
