"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentConfirmation = void 0;
const mongoose_1 = __importStar(require("mongoose"));
const paymentConfirmationSchema = new mongoose_1.Schema({
    user: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    order: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'Order',
        required: false
    },
    appointment: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'Appointment',
        required: false
    },
    amount: {
        type: Number,
        required: true,
        min: 0
    },
    paymentMethod: {
        type: String,
        required: true,
        trim: true
    },
    proofImage: {
        type: String,
        required: true
    },
    notes: {
        type: String,
        trim: true,
        maxlength: 500
    },
    status: {
        type: String,
        enum: ['pending', 'verified', 'rejected'],
        default: 'pending'
    },
    verifiedBy: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'User',
        required: false
    },
    verifiedAt: {
        type: Date,
        required: false
    },
    rejectionReason: {
        type: String,
        trim: true,
        maxlength: 200
    }
}, {
    timestamps: true
});
// Indexes for better query performance
paymentConfirmationSchema.index({ user: 1 });
paymentConfirmationSchema.index({ order: 1 });
paymentConfirmationSchema.index({ appointment: 1 });
paymentConfirmationSchema.index({ status: 1 });
paymentConfirmationSchema.index({ createdAt: -1 });
// Virtual for populated user data
paymentConfirmationSchema.virtual('userData', {
    ref: 'User',
    localField: 'user',
    foreignField: '_id',
    justOne: true
});
// Virtual for populated order data
paymentConfirmationSchema.virtual('orderData', {
    ref: 'Order',
    localField: 'order',
    foreignField: '_id',
    justOne: true
});
// Virtual for populated appointment data
paymentConfirmationSchema.virtual('appointmentData', {
    ref: 'Appointment',
    localField: 'appointment',
    foreignField: '_id',
    justOne: true
});
// Virtual for populated verifier data
paymentConfirmationSchema.virtual('verifierData', {
    ref: 'User',
    localField: 'verifiedBy',
    foreignField: '_id',
    justOne: true
});
// Ensure virtuals are included in JSON output
paymentConfirmationSchema.set('toJSON', { virtuals: true });
paymentConfirmationSchema.set('toObject', { virtuals: true });
// Custom validation to ensure either order or appointment is provided
paymentConfirmationSchema.pre('validate', function (next) {
    if (!this.order && !this.appointment) {
        next(new Error('Payment confirmation must be associated with either an order or appointment'));
    }
    else if (this.order && this.appointment) {
        next(new Error('Payment confirmation cannot be associated with both order and appointment'));
    }
    else {
        next();
    }
});
exports.PaymentConfirmation = mongoose_1.default.model('PaymentConfirmation', paymentConfirmationSchema);
