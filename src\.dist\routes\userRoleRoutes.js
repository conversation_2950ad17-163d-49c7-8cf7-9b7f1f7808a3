"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const controllers_1 = require("../controllers");
const auth_1 = require("../middleware/auth");
const validation_1 = require("../middleware/validation");
const validation_2 = require("../utils/validation");
const router = (0, express_1.Router)();
// All routes require admin authentication
router.use(auth_1.authenticate);
router.use((0, auth_1.authorize)('admin'));
// GET /api/admin/users
router.get('/', (0, validation_1.validate)(validation_2.paginationValidation), controllers_1.UserRoleController.getAllUsers);
// GET /api/admin/users/:id
router.get('/:id', (0, validation_1.validate)((0, validation_2.mongoIdValidation)()), controllers_1.UserRoleController.getUserById);
// PUT /api/admin/users/:id/role
router.put('/:id/role', (0, validation_1.validate)((0, validation_2.mongoIdValidation)()), controllers_1.UserRoleController.updateUserRole);
// PUT /api/admin/users/:id/deactivate
router.put('/:id/deactivate', (0, validation_1.validate)((0, validation_2.mongoIdValidation)()), controllers_1.UserRoleController.deactivateUser);
// PUT /api/admin/users/:id/activate
router.put('/:id/activate', (0, validation_1.validate)((0, validation_2.mongoIdValidation)()), controllers_1.UserRoleController.activateUser);
exports.default = router;
