"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const database_1 = require("../config/database");
// Setup test database connection
beforeAll(async () => {
    // Use test database
    process.env.MONGODB_URI = process.env.MONGODB_TEST_URI || 'mongodb://localhost:27017/microlocsbackend_test';
    await (0, database_1.connectDatabase)();
});
// Cleanup after tests
afterAll(async () => {
    await (0, database_1.disconnectDatabase)();
});
// Set test environment
process.env.NODE_ENV = 'test';
