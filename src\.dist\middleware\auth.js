"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.optionalAuth = exports.authorize = exports.authenticate = void 0;
const models_1 = require("../models");
const jwt_1 = require("../utils/jwt");
const services_1 = require("../services");
const response_1 = require("../utils/response");
const authenticate = async (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            (0, response_1.sendUnauthorized)(res, 'Access token is required');
            return;
        }
        const token = authHeader.substring(7); // Remove 'Bearer ' prefix
        if (!token) {
            (0, response_1.sendUnauthorized)(res, 'Access token is required');
            return;
        }
        // Check if token is blacklisted
        const isBlacklisted = await services_1.AuthService.isTokenBlacklisted(token);
        if (isBlacklisted) {
            (0, response_1.sendUnauthorized)(res, 'Token has been revoked');
            return;
        }
        const decoded = (0, jwt_1.verifyToken)(token);
        const user = await models_1.User.findById(decoded.userId).select('-password');
        if (!user) {
            (0, response_1.sendUnauthorized)(res, 'Invalid access token');
            return;
        }
        // Debug logging for auth middleware
        console.log('Auth middleware - User authenticated:');
        console.log('- User ID from token:', decoded.userId);
        console.log('- User ID from DB:', user._id.toString());
        console.log('- User ID type:', typeof user._id);
        req.user = user;
        next();
    }
    catch (error) {
        console.error('Authentication error:', error);
        (0, response_1.sendUnauthorized)(res, 'Invalid access token');
    }
};
exports.authenticate = authenticate;
const authorize = (...roles) => {
    return (req, res, next) => {
        if (!req.user) {
            (0, response_1.sendUnauthorized)(res, 'Authentication required');
            return;
        }
        if (!roles.includes(req.user.role)) {
            (0, response_1.sendForbidden)(res, 'Insufficient permissions');
            return;
        }
        next();
    };
};
exports.authorize = authorize;
const optionalAuth = async (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;
        if (authHeader && authHeader.startsWith('Bearer ')) {
            const token = authHeader.substring(7);
            if (token) {
                const decoded = (0, jwt_1.verifyToken)(token);
                const user = await models_1.User.findById(decoded.userId).select('-password');
                if (user) {
                    req.user = user;
                }
            }
        }
        next();
    }
    catch (error) {
        // Continue without authentication for optional auth
        next();
    }
};
exports.optionalAuth = optionalAuth;
