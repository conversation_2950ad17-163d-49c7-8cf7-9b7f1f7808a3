"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CacheService = void 0;
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
class CacheService {
    // Memory Cache Methods
    static setMemoryCache(key, data, ttl = 3600) {
        const expires = Date.now() + (ttl * 1000);
        this.memoryCache.set(key, { data, expires });
    }
    static getMemoryCache(key) {
        const cached = this.memoryCache.get(key);
        if (!cached)
            return null;
        if (Date.now() > cached.expires) {
            this.memoryCache.delete(key);
            return null;
        }
        return cached.data;
    }
    static deleteMemoryCache(key) {
        return this.memoryCache.delete(key);
    }
    static clearMemoryCache() {
        this.memoryCache.clear();
    }
    // File Cache Methods
    static setFileCache(key, data, ttl = 3600) {
        try {
            const cacheFile = path_1.default.join(this.cacheDir, `${key}.json`);
            const cacheData = {
                data,
                expires: Date.now() + (ttl * 1000),
                created: Date.now()
            };
            fs_1.default.writeFileSync(cacheFile, JSON.stringify(cacheData));
        }
        catch (error) {
            console.error('File cache write error:', error);
        }
    }
    static getFileCache(key) {
        try {
            const cacheFile = path_1.default.join(this.cacheDir, `${key}.json`);
            if (!fs_1.default.existsSync(cacheFile))
                return null;
            const cacheData = JSON.parse(fs_1.default.readFileSync(cacheFile, 'utf8'));
            if (Date.now() > cacheData.expires) {
                fs_1.default.unlinkSync(cacheFile);
                return null;
            }
            return cacheData.data;
        }
        catch (error) {
            console.error('File cache read error:', error);
            return null;
        }
    }
    static deleteFileCache(key) {
        try {
            const cacheFile = path_1.default.join(this.cacheDir, `${key}.json`);
            if (fs_1.default.existsSync(cacheFile)) {
                fs_1.default.unlinkSync(cacheFile);
                return true;
            }
            return false;
        }
        catch (error) {
            console.error('File cache delete error:', error);
            return false;
        }
    }
    // Image Cache Methods
    static clearImageCache() {
        try {
            const imageCacheDir = path_1.default.join(this.cacheDir, 'images');
            if (fs_1.default.existsSync(imageCacheDir)) {
                fs_1.default.rmSync(imageCacheDir, { recursive: true, force: true });
                fs_1.default.mkdirSync(imageCacheDir, { recursive: true });
            }
            return { success: true };
        }
        catch (error) {
            return { success: false, error: error.message };
        }
    }
    // Data Cache Methods
    static clearDataCache() {
        try {
            const dataCacheDir = path_1.default.join(this.cacheDir, 'data');
            if (fs_1.default.existsSync(dataCacheDir)) {
                fs_1.default.rmSync(dataCacheDir, { recursive: true, force: true });
                fs_1.default.mkdirSync(dataCacheDir, { recursive: true });
            }
            // Clear memory cache for data
            const dataKeys = Array.from(this.memoryCache.keys()).filter(key => key.startsWith('data:'));
            dataKeys.forEach(key => this.memoryCache.delete(key));
            return { success: true };
        }
        catch (error) {
            return { success: false, error: error.message };
        }
    }
    // Clear All Caches
    static clearAllCaches() {
        try {
            // Clear memory cache
            this.clearMemoryCache();
            // Clear file cache
            if (fs_1.default.existsSync(this.cacheDir)) {
                fs_1.default.rmSync(this.cacheDir, { recursive: true, force: true });
                fs_1.default.mkdirSync(this.cacheDir, { recursive: true });
                // Recreate subdirectories
                fs_1.default.mkdirSync(path_1.default.join(this.cacheDir, 'images'), { recursive: true });
                fs_1.default.mkdirSync(path_1.default.join(this.cacheDir, 'data'), { recursive: true });
            }
            return { success: true };
        }
        catch (error) {
            return { success: false, error: error.message };
        }
    }
    // Cache Statistics
    static getCacheStats() {
        const memoryCacheStats = {
            size: this.memoryCache.size,
            keys: Array.from(this.memoryCache.keys())
        };
        let fileCacheFiles = [];
        let diskUsage = 0;
        try {
            if (fs_1.default.existsSync(this.cacheDir)) {
                const files = fs_1.default.readdirSync(this.cacheDir, { recursive: true });
                fileCacheFiles = files.filter(file => typeof file === 'string');
                // Calculate disk usage
                const stats = fs_1.default.statSync(this.cacheDir);
                diskUsage = stats.size / (1024 * 1024); // Convert to MB
            }
        }
        catch (error) {
            console.error('Cache stats error:', error);
        }
        return {
            memoryCache: memoryCacheStats,
            fileCache: { size: fileCacheFiles.length, files: fileCacheFiles },
            diskUsage
        };
    }
    // Cleanup expired cache entries
    static cleanupExpiredCache() {
        let cleaned = 0;
        let errors = 0;
        // Cleanup memory cache
        for (const [key, value] of this.memoryCache.entries()) {
            if (Date.now() > value.expires) {
                this.memoryCache.delete(key);
                cleaned++;
            }
        }
        // Cleanup file cache
        try {
            if (fs_1.default.existsSync(this.cacheDir)) {
                const files = fs_1.default.readdirSync(this.cacheDir);
                for (const file of files) {
                    if (file.endsWith('.json')) {
                        try {
                            const filePath = path_1.default.join(this.cacheDir, file);
                            const cacheData = JSON.parse(fs_1.default.readFileSync(filePath, 'utf8'));
                            if (Date.now() > cacheData.expires) {
                                fs_1.default.unlinkSync(filePath);
                                cleaned++;
                            }
                        }
                        catch (error) {
                            errors++;
                            console.error(`Error cleaning cache file ${file}:`, error);
                        }
                    }
                }
            }
        }
        catch (error) {
            errors++;
            console.error('Cache cleanup error:', error);
        }
        return { cleaned, errors };
    }
    // Redis Cache Methods (for production with Redis)
    static async setRedisCache(key, data, ttl = 3600) {
        try {
            // In production, implement Redis caching
            // const redis = require('redis');
            // const client = redis.createClient();
            // await client.setEx(key, ttl, JSON.stringify(data));
            // Fallback to memory cache
            this.setMemoryCache(key, data, ttl);
            return true;
        }
        catch (error) {
            console.error('Redis cache error:', error);
            return false;
        }
    }
    static async getRedisCache(key) {
        try {
            // In production, implement Redis caching
            // const redis = require('redis');
            // const client = redis.createClient();
            // const cached = await client.get(key);
            // return cached ? JSON.parse(cached) : null;
            // Fallback to memory cache
            return this.getMemoryCache(key);
        }
        catch (error) {
            console.error('Redis cache error:', error);
            return null;
        }
    }
}
exports.CacheService = CacheService;
_a = CacheService;
CacheService.memoryCache = new Map();
CacheService.cacheDir = path_1.default.join(process.cwd(), 'cache');
(() => {
    // Ensure cache directory exists
    if (!fs_1.default.existsSync(_a.cacheDir)) {
        fs_1.default.mkdirSync(_a.cacheDir, { recursive: true });
    }
})();
