import { useState } from 'react';
import { saveLoginResponseData, dashboardAPI, getCurrentUser, isAuthenticated } from '../utils/api';

// Example component showing how to handle the login response you provided
export default function LoginExample() {
  const [dashboardData, setDashboardData] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Your login response data
  const exampleLoginResponse = {
    "success": true,
    "message": "Login successful",
    "data": {
      "user": {
        "notificationPreferences": {
          "email": true,
          "sms": false,
          "push": true
        },
        "_id": "685930e41037604a323737e7",
        "name": "Joshua Olawore",
        "firstName": "Joshua",
        "lastName": "Olawore",
        "email": "<EMAIL>",
        "phone": "+2347049670618",
        "role": "user",
        "isVerified": false,
        "favorites": [],
        "createdAt": "2025-06-23T10:48:04.934Z",
        "updatedAt": "2025-06-27T17:55:03.344Z",
        "__v": 0
      },
      "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************.CJqCswkXSgi51bDHOFdU_mw-KEXCaeT55bb0AgWsjUI",
      "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************.Sctjw_7vl5Fr8rm7R8YDRi1C7eh9UgIXNVRGCsUwk3A"
    }
  };

  // Function to simulate saving login data and calling dashboard
  const handleSaveLoginAndCallDashboard = async () => {
    try {
      setLoading(true);
      setError(null);

      // Step 1: Save the login response data to localStorage
      console.log('Saving login response data...');
      saveLoginResponseData(exampleLoginResponse);

      // Step 2: Verify data was saved
      const currentUser = getCurrentUser();
      const isAuth = isAuthenticated();
      console.log('Current user:', currentUser);
      console.log('Is authenticated:', isAuth);

      // Step 3: Call the dashboard endpoint
      console.log('Calling dashboard API...');
      const dashboard = await dashboardAPI.getUserDashboard();
      setDashboardData(dashboard);
      console.log('Dashboard data:', dashboard);

    } catch (err: any) {
      console.error('Error:', err);
      setError(err.message || 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  // Function to call dashboard with stored data
  const handleCallDashboard = async () => {
    try {
      setLoading(true);
      setError(null);

      const dashboard = await dashboardAPI.getUserDashboard();
      setDashboardData(dashboard);
      console.log('Dashboard data:', dashboard);

      // Log specific parts of the response for easier debugging
      if (dashboard && dashboard.success && dashboard.data) {
        console.log('User data:', dashboard.data.user);
        console.log('Appointments:', dashboard.data.appointments);
        console.log('Statistics:', dashboard.data.statistics);
      }

    } catch (err: any) {
      console.error('Error calling dashboard:', err);
      setError(err.message || 'Failed to fetch dashboard data');
    } finally {
      setLoading(false);
    }
  };

  // Function to call user appointments
  const handleCallUserAppointments = async () => {
    try {
      setLoading(true);
      setError(null);

      const appointments = await dashboardAPI.getUserAppointments();
      setDashboardData(appointments);
      console.log('User appointments:', appointments);

    } catch (err: any) {
      console.error('Error calling user appointments:', err);
      setError(err.message || 'Failed to fetch user appointments');
    } finally {
      setLoading(false);
    }
  };

  // Function to simulate the dashboard response you provided
  const handleSimulateDashboardResponse = () => {
    const simulatedDashboardResponse = {
      "success": true,
      "message": "User dashboard data retrieved successfully",
      "data": {
        "user": {
          "notificationPreferences": {
            "email": true,
            "sms": false,
            "push": true
          },
          "_id": "685930e41037604a323737e7",
          "name": "Joshua Olawore",
          "firstName": "Joshua",
          "lastName": "Olawore",
          "email": "<EMAIL>",
          "phone": "+2347049670618",
          "role": "user",
          "isVerified": false,
          "favorites": [],
          "createdAt": "2025-06-23T10:48:04.934Z",
          "updatedAt": "2025-06-27T17:55:03.344Z",
          "__v": 0
        },
        "appointments": {
          "all": [
            {
              "id": "68a036e0af1d8c7a346861c3",
              "serviceId": "68a02192a93d00184baaee7a",
              "serviceName": "4-5 week Retightening - Small",
              "servicePrice": 200,
              "serviceDuration": 150,
              "serviceCategory": "4-5 weeks",
              "date": "2025-08-19T15:00:00.000Z",
              "time": "4:00 PM",
              "status": "pending",
              "customerInfo": {
                "name": "Joshua Olawore",
                "email": "<EMAIL>",
                "phone": "+1107049670618"
              },
              "notes": "",
              "createdAt": "2025-08-16T07:44:32.841Z",
              "updatedAt": "2025-08-16T07:44:32.841Z"
            }
          ],
          "recent": [
            {
              "id": "68a036e0af1d8c7a346861c3",
              "serviceId": "68a02192a93d00184baaee7a",
              "serviceName": "4-5 week Retightening - Small",
              "servicePrice": 200,
              "serviceDuration": 150,
              "serviceCategory": "4-5 weeks",
              "date": "2025-08-19T15:00:00.000Z",
              "time": "4:00 PM",
              "status": "pending",
              "customerInfo": {
                "name": "Joshua Olawore",
                "email": "<EMAIL>",
                "phone": "+1107049670618"
              },
              "notes": "",
              "createdAt": "2025-08-16T07:44:32.841Z",
              "updatedAt": "2025-08-16T07:44:32.841Z"
            }
          ],
          "upcoming": [
            {
              "id": "68a036e0af1d8c7a346861c3",
              "serviceId": "68a02192a93d00184baaee7a",
              "serviceName": "4-5 week Retightening - Small",
              "servicePrice": 200,
              "serviceDuration": 150,
              "date": "2025-08-19T15:00:00.000Z",
              "time": "4:00 PM",
              "status": "pending",
              "notes": ""
            }
          ]
        },
        "statistics": {
          "appointments": {
            "total": 1,
            "pending": 1,
            "confirmed": 0,
            "completed": 0,
            "cancelled": 0
          },
          "totalSpent": 0,
          "favoriteServices": [
            {
              "id": "68a02192a93d00184baaee7a",
              "name": "4-5 week Retightening - Small",
              "count": 1,
              "price": 200,
              "category": "4-5 weeks"
            }
          ],
          "memberSince": "2025-06-23T10:48:04.934Z",
          "lastActivity": "2025-06-27T17:55:03.344Z"
        }
      }
    };

    setDashboardData(simulatedDashboardResponse);
    console.log('Simulated dashboard response:', simulatedDashboardResponse);
  };

  // Function to call public dashboard API
  const handleCallDashboardPublic = async () => {
    try {
      setLoading(true);
      setError(null);

      const dashboard = await dashboardAPI.getUserDashboardPublic();
      setDashboardData(dashboard);
      console.log('Public dashboard data:', dashboard);

    } catch (err: any) {
      console.error('Error calling public dashboard:', err);
      setError(err.message || 'Failed to fetch public dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const currentUser = getCurrentUser();
  const isAuth = isAuthenticated();

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <h2>Login Response Handler Example</h2>
      
      {/* Current Auth Status */}
      <div style={{ marginBottom: '20px', padding: '15px', backgroundColor: '#f5f5f5', borderRadius: '5px' }}>
        <h3>Current Authentication Status</h3>
        <p><strong>Is Authenticated:</strong> {isAuth ? 'Yes' : 'No'}</p>
        {currentUser && (
          <div>
            <p><strong>User ID:</strong> {currentUser._id || currentUser.id}</p>
            <p><strong>Name:</strong> {currentUser.name || `${currentUser.firstName} ${currentUser.lastName}`}</p>
            <p><strong>Email:</strong> {currentUser.email}</p>
            <p><strong>Role:</strong> {currentUser.role}</p>
          </div>
        )}
      </div>

      {/* Action Buttons */}
      <div style={{ marginBottom: '20px' }}>
        <button 
          onClick={handleSaveLoginAndCallDashboard}
          disabled={loading}
          style={{ 
            marginRight: '10px', 
            padding: '10px 15px', 
            backgroundColor: '#007bff', 
            color: 'white', 
            border: 'none', 
            borderRadius: '5px',
            cursor: loading ? 'not-allowed' : 'pointer'
          }}
        >
          {loading ? 'Processing...' : 'Save Login Data & Call Dashboard'}
        </button>

        <button 
          onClick={handleCallDashboard}
          disabled={loading || !isAuth}
          style={{ 
            marginRight: '10px', 
            padding: '10px 15px', 
            backgroundColor: '#28a745', 
            color: 'white', 
            border: 'none', 
            borderRadius: '5px',
            cursor: (loading || !isAuth) ? 'not-allowed' : 'pointer'
          }}
        >
          Call Dashboard API
        </button>

        <button
          onClick={handleCallUserAppointments}
          disabled={loading || !isAuth}
          style={{
            marginRight: '10px',
            padding: '10px 15px',
            backgroundColor: '#17a2b8',
            color: 'white',
            border: 'none',
            borderRadius: '5px',
            cursor: (loading || !isAuth) ? 'not-allowed' : 'pointer'
          }}
        >
          Call User Appointments API
        </button>

        <button
          onClick={handleSimulateDashboardResponse}
          disabled={loading}
          style={{
            marginRight: '10px',
            padding: '10px 15px',
            backgroundColor: '#6f42c1',
            color: 'white',
            border: 'none',
            borderRadius: '5px',
            cursor: loading ? 'not-allowed' : 'pointer'
          }}
        >
          Simulate Dashboard Response
        </button>

        <button
          onClick={handleCallDashboardPublic}
          disabled={loading}
          style={{
            padding: '10px 15px',
            backgroundColor: '#fd7e14',
            color: 'white',
            border: 'none',
            borderRadius: '5px',
            cursor: loading ? 'not-allowed' : 'pointer'
          }}
        >
          Call Public Dashboard API
        </button>
      </div>

      {/* Error Display */}
      {error && (
        <div style={{ 
          marginBottom: '20px', 
          padding: '15px', 
          backgroundColor: '#f8d7da', 
          color: '#721c24', 
          borderRadius: '5px',
          border: '1px solid #f5c6cb'
        }}>
          <strong>Error:</strong> {error}
        </div>
      )}

      {/* Dashboard Data Display */}
      {dashboardData && (
        <div style={{ 
          marginTop: '20px', 
          padding: '15px', 
          backgroundColor: '#d4edda', 
          borderRadius: '5px',
          border: '1px solid #c3e6cb'
        }}>
          <h3>Dashboard Response:</h3>
          <pre style={{ 
            backgroundColor: '#f8f9fa', 
            padding: '10px', 
            borderRadius: '3px', 
            overflow: 'auto',
            fontSize: '12px'
          }}>
            {JSON.stringify(dashboardData, null, 2)}
          </pre>
        </div>
      )}

      {/* Example Login Response Display */}
      <div style={{ 
        marginTop: '30px', 
        padding: '15px', 
        backgroundColor: '#e2e3e5', 
        borderRadius: '5px'
      }}>
        <h3>Example Login Response (Your Data):</h3>
        <pre style={{ 
          backgroundColor: '#f8f9fa', 
          padding: '10px', 
          borderRadius: '3px', 
          overflow: 'auto',
          fontSize: '11px'
        }}>
          {JSON.stringify(exampleLoginResponse, null, 2)}
        </pre>
      </div>
    </div>
  );
}
