"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ServiceCategoryController = void 0;
const models_1 = require("../models");
const response_1 = require("../utils/response");
class ServiceCategoryController {
    static async getServiceCategories(req, res) {
        try {
            // Get unique categories from services
            const categories = await models_1.Service.aggregate([
                { $match: { isActive: true } },
                { $group: { _id: '$category', count: { $sum: 1 } } },
                { $project: { name: '$_id', count: 1, _id: 0 } },
                { $sort: { name: 1 } }
            ]);
            (0, response_1.sendSuccess)(res, 'Service categories retrieved successfully', categories);
        }
        catch (error) {
            console.error('Get service categories error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async createServiceCategory(req, res) {
        try {
            const { name, description } = req.body;
            // Check if category already exists
            const existingService = await models_1.Service.findOne({ category: name });
            if (existingService) {
                (0, response_1.sendError)(res, 'Service category already exists');
                return;
            }
            // Create a placeholder service for the category (or you could create a separate Category model)
            const categoryData = {
                name: `${name} Category`,
                description: description || `Services in the ${name} category`,
                category: name,
                price: 0,
                duration: 0,
                isActive: false // This is just a placeholder
            };
            (0, response_1.sendCreated)(res, 'Service category created successfully', { name, description });
        }
        catch (error) {
            console.error('Create service category error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async updateServiceCategory(req, res) {
        try {
            const { id } = req.params;
            const { name, description } = req.body;
            // Update all services in this category
            const result = await models_1.Service.updateMany({ category: id }, { category: name });
            (0, response_1.sendSuccess)(res, 'Service category updated successfully', {
                oldCategory: id,
                newCategory: name,
                servicesUpdated: result.modifiedCount
            });
        }
        catch (error) {
            console.error('Update service category error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async deleteServiceCategory(req, res) {
        try {
            const { id } = req.params;
            // Check if any services use this category
            const servicesCount = await models_1.Service.countDocuments({ category: id });
            if (servicesCount > 0) {
                (0, response_1.sendError)(res, `Cannot delete category. ${servicesCount} services are using this category.`);
                return;
            }
            (0, response_1.sendSuccess)(res, 'Service category deleted successfully');
        }
        catch (error) {
            console.error('Delete service category error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
}
exports.ServiceCategoryController = ServiceCategoryController;
