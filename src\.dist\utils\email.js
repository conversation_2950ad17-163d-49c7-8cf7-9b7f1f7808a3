"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.emailTemplates = exports.sendEmail = void 0;
const nodemailer_1 = __importDefault(require("nodemailer"));
const config_1 = require("../config");
// Create transporter
const createTransporter = () => {
    return nodemailer_1.default.createTransport({
        host: config_1.config.EMAIL.HOST,
        port: config_1.config.EMAIL.PORT,
        secure: false, // true for 465, false for other ports
        auth: {
            user: config_1.config.EMAIL.USER,
            pass: config_1.config.EMAIL.PASS,
        },
    });
};
const sendEmail = async (options) => {
    try {
        const transporter = createTransporter();
        const mailOptions = {
            from: config_1.config.EMAIL.FROM,
            to: options.to,
            subject: options.subject,
            html: options.html,
            text: options.text,
        };
        await transporter.sendMail(mailOptions);
        console.log(`Email sent successfully to ${options.to}`);
    }
    catch (error) {
        console.error('Email sending failed:', error);
        throw new Error('Failed to send email');
    }
};
exports.sendEmail = sendEmail;
// Email templates
exports.emailTemplates = {
    welcome: (name) => ({
        subject: 'Welcome to MicroLocs!',
        html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #333;">Welcome to MicroLocs, ${name}!</h2>
        <p>Thank you for joining our platform. We're excited to have you on board!</p>
        <p>You can now:</p>
        <ul>
          <li>Book appointments with our professionals</li>
          <li>Browse and purchase our products</li>
          <li>Manage your profile and preferences</li>
        </ul>
        <p>If you have any questions, feel free to contact our support team.</p>
        <p>Best regards,<br>The MicroLocs Team</p>
      </div>
    `,
        text: `Welcome to MicroLocs, ${name}! Thank you for joining our platform.`
    }),
    passwordReset: (name, resetToken) => ({
        subject: 'Password Reset Request',
        html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #333;">Password Reset Request</h2>
        <p>Hello ${name},</p>
        <p>You requested a password reset for your MicroLocs account.</p>
        <p>Your reset token is: <strong>${resetToken}</strong></p>
        <p>This token will expire in 1 hour.</p>
        <p>If you didn't request this reset, please ignore this email.</p>
        <p>Best regards,<br>The MicroLocs Team</p>
      </div>
    `,
        text: `Password reset token: ${resetToken}. This token will expire in 1 hour.`
    }),
    appointmentConfirmation: (name, service, date, time) => ({
        subject: 'Appointment Confirmation',
        html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #333;">Appointment Confirmed</h2>
        <p>Hello ${name},</p>
        <p>Your appointment has been confirmed!</p>
        <div style="background-color: #f5f5f5; padding: 20px; margin: 20px 0; border-radius: 5px;">
          <p><strong>Service:</strong> ${service}</p>
          <p><strong>Date:</strong> ${date}</p>
          <p><strong>Time:</strong> ${time}</p>
        </div>
        <p>Please arrive 10 minutes early for your appointment.</p>
        <p>Best regards,<br>The MicroLocs Team</p>
      </div>
    `,
        text: `Appointment confirmed for ${service} on ${date} at ${time}.`
    }),
    orderConfirmation: (name, orderNumber, totalAmount) => ({
        subject: 'Order Confirmation',
        html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #333;">Order Confirmed</h2>
        <p>Hello ${name},</p>
        <p>Thank you for your order!</p>
        <div style="background-color: #f5f5f5; padding: 20px; margin: 20px 0; border-radius: 5px;">
          <p><strong>Order Number:</strong> ${orderNumber}</p>
          <p><strong>Total Amount:</strong> $${totalAmount.toFixed(2)}</p>
        </div>
        <p>We'll send you another email when your order ships.</p>
        <p>Best regards,<br>The MicroLocs Team</p>
      </div>
    `,
        text: `Order ${orderNumber} confirmed. Total: $${totalAmount.toFixed(2)}.`
    })
};
