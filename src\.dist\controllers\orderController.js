"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrderController = void 0;
const models_1 = require("../models");
const response_1 = require("../utils/response");
const email_1 = require("../utils/email");
class OrderController {
    static async createOrder(req, res) {
        try {
            if (!req.user) {
                (0, response_1.sendError)(res, 'Authentication required', undefined, 401);
                return;
            }
            const { items, shippingAddress, paymentMethod } = req.body;
            // Validate and calculate order items
            const orderItems = [];
            let totalAmount = 0;
            for (const item of items) {
                const product = await models_1.Product.findById(item.productId);
                if (!product || !product.isActive) {
                    (0, response_1.sendError)(res, `Product ${item.productId} not found or inactive`);
                    return;
                }
                if (product.stock < item.quantity) {
                    (0, response_1.sendError)(res, `Insufficient stock for product ${product.name}`);
                    return;
                }
                const orderItem = {
                    product: item.productId,
                    quantity: item.quantity,
                    price: product.price,
                    name: product.name
                };
                orderItems.push(orderItem);
                totalAmount += product.price * item.quantity;
            }
            // Create order
            const order = await models_1.Order.create({
                user: req.user._id,
                items: orderItems,
                totalAmount,
                shippingAddress,
                paymentMethod
            });
            // Update product stock
            for (const item of items) {
                await models_1.Product.findByIdAndUpdate(item.productId, { $inc: { stock: -item.quantity } });
            }
            // Clear user's cart
            await models_1.Cart.findOneAndUpdate({ user: req.user._id }, { items: [] });
            await order.populate('items.product', 'name images');
            // Send order confirmation email
            try {
                const emailContent = email_1.emailTemplates.orderConfirmation(req.user.name, order.orderNumber, order.totalAmount);
                await (0, email_1.sendEmail)({
                    to: req.user.email,
                    subject: emailContent.subject,
                    html: emailContent.html,
                    text: emailContent.text
                });
            }
            catch (error) {
                console.error('Failed to send order confirmation email:', error);
            }
            (0, response_1.sendCreated)(res, 'Order created successfully', order);
        }
        catch (error) {
            console.error('Create order error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async getUserOrders(req, res) {
        try {
            if (!req.user) {
                (0, response_1.sendError)(res, 'Authentication required', undefined, 401);
                return;
            }
            const { page = 1, limit = 10 } = req.query;
            const pageNum = Number(page);
            const limitNum = Number(limit);
            const skip = (pageNum - 1) * limitNum;
            const [orders, total] = await Promise.all([
                models_1.Order.find({ user: req.user._id })
                    .populate('items.product', 'name images')
                    .sort({ createdAt: -1 })
                    .skip(skip)
                    .limit(limitNum),
                models_1.Order.countDocuments({ user: req.user._id })
            ]);
            const totalPages = Math.ceil(total / limitNum);
            (0, response_1.sendSuccess)(res, 'Orders retrieved successfully', {
                orders,
                pagination: {
                    currentPage: pageNum,
                    totalPages,
                    totalItems: total,
                    itemsPerPage: limitNum,
                    hasNextPage: pageNum < totalPages,
                    hasPrevPage: pageNum > 1
                }
            });
        }
        catch (error) {
            console.error('Get user orders error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async getOrderById(req, res) {
        try {
            if (!req.user) {
                (0, response_1.sendError)(res, 'Authentication required', undefined, 401);
                return;
            }
            const { id } = req.params;
            const order = await models_1.Order.findOne({
                _id: id,
                user: req.user._id
            }).populate('items.product', 'name images');
            if (!order) {
                (0, response_1.sendNotFound)(res, 'Order not found');
                return;
            }
            (0, response_1.sendSuccess)(res, 'Order retrieved successfully', order);
        }
        catch (error) {
            console.error('Get order by ID error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async updateOrderStatus(req, res) {
        try {
            const { id } = req.params;
            const { status } = req.body;
            const order = await models_1.Order.findByIdAndUpdate(id, { status }, { new: true, runValidators: true }).populate('user', 'name email')
                .populate('items.product', 'name images');
            if (!order) {
                (0, response_1.sendNotFound)(res, 'Order not found');
                return;
            }
            (0, response_1.sendSuccess)(res, 'Order status updated successfully', order);
        }
        catch (error) {
            console.error('Update order status error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async getAllOrders(req, res) {
        try {
            const { page = 1, limit = 20, status, search } = req.query;
            const filter = {};
            if (status) {
                filter.status = status;
            }
            if (search) {
                filter.orderNumber = { $regex: search, $options: 'i' };
            }
            const pageNum = Number(page);
            const limitNum = Number(limit);
            const skip = (pageNum - 1) * limitNum;
            const [orders, total] = await Promise.all([
                models_1.Order.find(filter)
                    .populate('user', 'name email phone')
                    .populate('items.product', 'name images')
                    .sort({ createdAt: -1 })
                    .skip(skip)
                    .limit(limitNum),
                models_1.Order.countDocuments(filter)
            ]);
            const totalPages = Math.ceil(total / limitNum);
            (0, response_1.sendSuccess)(res, 'Orders retrieved successfully', {
                orders,
                pagination: {
                    currentPage: pageNum,
                    totalPages,
                    totalItems: total,
                    itemsPerPage: limitNum,
                    hasNextPage: pageNum < totalPages,
                    hasPrevPage: pageNum > 1
                }
            });
        }
        catch (error) {
            console.error('Get all orders error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
}
exports.OrderController = OrderController;
