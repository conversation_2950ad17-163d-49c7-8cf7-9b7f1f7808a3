"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserController = void 0;
const models_1 = require("../models");
const response_1 = require("../utils/response");
class UserController {
    static async getProfile(req, res) {
        try {
            if (!req.user) {
                (0, response_1.sendError)(res, 'Authentication required', undefined, 401);
                return;
            }
            const user = await models_1.User.findById(req.user._id)
                .populate('favorites', 'name price images rating');
            if (!user) {
                (0, response_1.sendNotFound)(res, 'User not found');
                return;
            }
            (0, response_1.sendSuccess)(res, 'Profile retrieved successfully', user);
        }
        catch (error) {
            console.error('Get profile error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async updateProfile(req, res) {
        try {
            if (!req.user) {
                (0, response_1.sendError)(res, 'Authentication required', undefined, 401);
                return;
            }
            const { name, firstName, lastName, email, phone, notificationPreferences } = req.body;
            const updateData = {};
            if (name)
                updateData.name = name;
            if (firstName)
                updateData.firstName = firstName;
            if (lastName)
                updateData.lastName = lastName;
            if (phone)
                updateData.phone = phone;
            if (notificationPreferences)
                updateData.notificationPreferences = notificationPreferences;
            // Handle email update with uniqueness check
            if (email && email !== req.user.email) {
                const existingUser = await models_1.User.findOne({ email, _id: { $ne: req.user._id } });
                if (existingUser) {
                    (0, response_1.sendError)(res, 'Email address is already in use', undefined, 400);
                    return;
                }
                updateData.email = email;
            }
            const user = await models_1.User.findByIdAndUpdate(req.user._id, updateData, { new: true, runValidators: true }).populate('favorites', 'name price images rating');
            if (!user) {
                (0, response_1.sendNotFound)(res, 'User not found');
                return;
            }
            (0, response_1.sendSuccess)(res, 'Profile updated successfully', user);
        }
        catch (error) {
            console.error('Update profile error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async getFavorites(req, res) {
        try {
            if (!req.user) {
                (0, response_1.sendError)(res, 'Authentication required', undefined, 401);
                return;
            }
            const user = await models_1.User.findById(req.user._id)
                .populate('favorites', 'name price images rating category stock isActive');
            if (!user) {
                (0, response_1.sendNotFound)(res, 'User not found');
                return;
            }
            // Filter out inactive products
            const activeFavorites = user.favorites.filter((product) => product.isActive);
            (0, response_1.sendSuccess)(res, 'Favorites retrieved successfully', activeFavorites);
        }
        catch (error) {
            console.error('Get favorites error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async addToFavorites(req, res) {
        try {
            if (!req.user) {
                (0, response_1.sendError)(res, 'Authentication required', undefined, 401);
                return;
            }
            const { productId } = req.body;
            // Verify product exists
            const product = await models_1.Product.findById(productId);
            if (!product || !product.isActive) {
                (0, response_1.sendNotFound)(res, 'Product not found or inactive');
                return;
            }
            const user = await models_1.User.findById(req.user._id);
            if (!user) {
                (0, response_1.sendNotFound)(res, 'User not found');
                return;
            }
            // Check if already in favorites
            if (user.favorites.includes(productId)) {
                (0, response_1.sendError)(res, 'Product already in favorites');
                return;
            }
            user.favorites.push(productId);
            await user.save();
            await user.populate('favorites', 'name price images rating category');
            (0, response_1.sendSuccess)(res, 'Product added to favorites successfully', user.favorites);
        }
        catch (error) {
            console.error('Add to favorites error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async removeFromFavorites(req, res) {
        try {
            if (!req.user) {
                (0, response_1.sendError)(res, 'Authentication required', undefined, 401);
                return;
            }
            const { productId } = req.params;
            const user = await models_1.User.findById(req.user._id);
            if (!user) {
                (0, response_1.sendNotFound)(res, 'User not found');
                return;
            }
            user.favorites = user.favorites.filter((fav) => fav.toString() !== productId);
            await user.save();
            await user.populate('favorites', 'name price images rating category');
            (0, response_1.sendSuccess)(res, 'Product removed from favorites successfully', user.favorites);
        }
        catch (error) {
            console.error('Remove from favorites error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async updateNotificationPreferences(req, res) {
        try {
            if (!req.user) {
                (0, response_1.sendError)(res, 'Authentication required', undefined, 401);
                return;
            }
            const { email, sms, push } = req.body;
            const user = await models_1.User.findByIdAndUpdate(req.user._id, {
                notificationPreferences: {
                    email: email !== undefined ? email : req.user.notificationPreferences.email,
                    sms: sms !== undefined ? sms : req.user.notificationPreferences.sms,
                    push: push !== undefined ? push : req.user.notificationPreferences.push
                }
            }, { new: true, runValidators: true });
            if (!user) {
                (0, response_1.sendNotFound)(res, 'User not found');
                return;
            }
            (0, response_1.sendSuccess)(res, 'Notification preferences updated successfully', user.notificationPreferences);
        }
        catch (error) {
            console.error('Update notification preferences error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async changePassword(req, res) {
        try {
            if (!req.user) {
                (0, response_1.sendError)(res, 'Authentication required', undefined, 401);
                return;
            }
            const { currentPassword, newPassword, confirmPassword } = req.body;
            if (!currentPassword || !newPassword || !confirmPassword) {
                (0, response_1.sendError)(res, 'All password fields are required');
                return;
            }
            if (newPassword !== confirmPassword) {
                (0, response_1.sendError)(res, 'New passwords do not match');
                return;
            }
            if (newPassword.length < 6) {
                (0, response_1.sendError)(res, 'New password must be at least 6 characters long');
                return;
            }
            // Get user with password field
            const user = await models_1.User.findById(req.user._id).select('+password');
            if (!user) {
                (0, response_1.sendNotFound)(res, 'User not found');
                return;
            }
            // Verify current password
            const isCurrentPasswordValid = await user.comparePassword(currentPassword);
            if (!isCurrentPasswordValid) {
                (0, response_1.sendError)(res, 'Current password is incorrect', undefined, 400);
                return;
            }
            // Update password
            user.password = newPassword;
            await user.save();
            (0, response_1.sendSuccess)(res, 'Password changed successfully');
        }
        catch (error) {
            console.error('Change password error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
}
exports.UserController = UserController;
