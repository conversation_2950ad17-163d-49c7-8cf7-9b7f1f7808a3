"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const controllers_1 = require("../controllers");
const auth_1 = require("../middleware/auth");
const validation_1 = require("../middleware/validation");
const validation_2 = require("../utils/validation");
const router = (0, express_1.Router)();
// GET /api/users/profile
router.get('/profile', auth_1.authenticate, controllers_1.UserController.getProfile);
// PUT /api/users/profile
router.put('/profile', auth_1.authenticate, controllers_1.UserController.updateProfile);
// GET /api/users/favorites
router.get('/favorites', auth_1.authenticate, controllers_1.UserController.getFavorites);
// POST /api/users/favorites
router.post('/favorites', auth_1.authenticate, controllers_1.UserController.addToFavorites);
// DELETE /api/users/favorites/:productId
router.delete('/favorites/:productId', auth_1.authenticate, (0, validation_1.validate)((0, validation_2.mongoIdValidation)('productId')), controllers_1.UserController.removeFromFavorites);
// PUT /api/users/notification-preferences
router.put('/notification-preferences', auth_1.authenticate, controllers_1.UserController.updateNotificationPreferences);
// PUT /api/users/password
router.put('/password', auth_1.authenticate, controllers_1.UserController.changePassword);
exports.default = router;
