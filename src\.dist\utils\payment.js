"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentService = void 0;
class PaymentService {
    static isStripeConfigured() {
        return !!(process.env.STRIPE_SECRET_KEY && process.env.STRIPE_PUBLISHABLE_KEY);
    }
    static async createPaymentIntent(amount, currency = 'usd', metadata) {
        try {
            if (!this.isStripeConfigured()) {
                // Mock payment intent for development
                const mockPaymentIntent = {
                    id: `pi_mock_${Date.now()}`,
                    amount,
                    currency,
                    status: 'requires_payment_method',
                    client_secret: `pi_mock_${Date.now()}_secret_mock`
                };
                console.log(`Mock payment intent created: ${mockPaymentIntent.id} for $${amount / 100}`);
                return { success: true, paymentIntent: mockPaymentIntent };
            }
            // Production Stripe implementation
            const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
            const paymentIntent = await stripe.paymentIntents.create({
                amount,
                currency,
                metadata: metadata || {},
                automatic_payment_methods: {
                    enabled: true,
                },
            });
            return {
                success: true,
                paymentIntent: {
                    id: paymentIntent.id,
                    amount: paymentIntent.amount,
                    currency: paymentIntent.currency,
                    status: paymentIntent.status,
                    client_secret: paymentIntent.client_secret
                }
            };
        }
        catch (error) {
            console.error('Payment intent creation error:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }
    static async confirmPaymentIntent(paymentIntentId, paymentMethodId) {
        try {
            if (!this.isStripeConfigured()) {
                // Mock payment confirmation
                const mockPaymentIntent = {
                    id: paymentIntentId,
                    amount: 0, // Would be retrieved from database in real implementation
                    currency: 'usd',
                    status: 'succeeded'
                };
                console.log(`Mock payment confirmed: ${paymentIntentId}`);
                return { success: true, paymentIntent: mockPaymentIntent };
            }
            // Production Stripe implementation
            const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
            const confirmParams = {};
            if (paymentMethodId) {
                confirmParams.payment_method = paymentMethodId;
            }
            const paymentIntent = await stripe.paymentIntents.confirm(paymentIntentId, confirmParams);
            return {
                success: true,
                paymentIntent: {
                    id: paymentIntent.id,
                    amount: paymentIntent.amount,
                    currency: paymentIntent.currency,
                    status: paymentIntent.status
                }
            };
        }
        catch (error) {
            console.error('Payment confirmation error:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }
    static async retrievePaymentIntent(paymentIntentId) {
        try {
            if (!this.isStripeConfigured()) {
                // Mock payment intent retrieval
                const mockPaymentIntent = {
                    id: paymentIntentId,
                    amount: 0,
                    currency: 'usd',
                    status: 'succeeded'
                };
                return { success: true, paymentIntent: mockPaymentIntent };
            }
            // Production Stripe implementation
            const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
            const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);
            return {
                success: true,
                paymentIntent: {
                    id: paymentIntent.id,
                    amount: paymentIntent.amount,
                    currency: paymentIntent.currency,
                    status: paymentIntent.status
                }
            };
        }
        catch (error) {
            console.error('Payment intent retrieval error:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }
    static async createRefund(refundRequest) {
        try {
            if (!this.isStripeConfigured()) {
                // Mock refund
                const mockRefund = {
                    id: `re_mock_${Date.now()}`,
                    amount: refundRequest.amount || 0,
                    status: 'succeeded',
                    reason: refundRequest.reason
                };
                console.log(`Mock refund created: ${mockRefund.id}`);
                return { success: true, refund: mockRefund };
            }
            // Production Stripe implementation
            const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
            const refundParams = {
                payment_intent: refundRequest.paymentIntentId
            };
            if (refundRequest.amount) {
                refundParams.amount = refundRequest.amount;
            }
            if (refundRequest.reason) {
                refundParams.reason = refundRequest.reason;
            }
            const refund = await stripe.refunds.create(refundParams);
            return {
                success: true,
                refund: {
                    id: refund.id,
                    amount: refund.amount,
                    status: refund.status,
                    reason: refund.reason
                }
            };
        }
        catch (error) {
            console.error('Refund creation error:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }
    static async processGiftCardPayment(giftCardCode, amount) {
        try {
            // This would integrate with the gift card controller
            const { GiftCardController } = await Promise.resolve().then(() => __importStar(require('../controllers/giftCardController')));
            const result = await GiftCardController.redeemGiftCard(giftCardCode, amount);
            if (result.success) {
                return {
                    success: true,
                    remainingBalance: result.remainingBalance
                };
            }
            else {
                return {
                    success: false,
                    error: result.error
                };
            }
        }
        catch (error) {
            console.error('Gift card payment error:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }
    static calculateTax(amount, taxRate) {
        return Math.round(amount * taxRate);
    }
    static calculateShipping(amount, shippingRate, freeShippingThreshold) {
        if (freeShippingThreshold && amount >= freeShippingThreshold) {
            return 0;
        }
        return shippingRate;
    }
    static formatCurrency(amount, currency = 'USD') {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: currency.toUpperCase(),
        }).format(amount / 100);
    }
}
exports.PaymentService = PaymentService;
