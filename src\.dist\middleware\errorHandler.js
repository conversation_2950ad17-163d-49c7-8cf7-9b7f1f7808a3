"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.notFound = exports.errorHandler = void 0;
const response_1 = require("../utils/response");
const errorHandler = (err, req, res, next) => {
    let error = { ...err };
    error.message = err.message;
    // Log error
    console.error('Error:', err);
    // Mongoose bad ObjectId
    if (err.name === 'CastError') {
        const message = 'Resource not found';
        error = { ...error, message, statusCode: 404 };
    }
    // Mongoose duplicate key
    if (err.code === 11000) {
        const field = Object.keys(err.keyValue)[0];
        const message = `${field} already exists`;
        error = { ...error, message, statusCode: 400 };
    }
    // Mongoose validation error
    if (err.name === 'ValidationError') {
        const message = Object.values(err.errors).map((val) => val.message).join(', ');
        error = { ...error, message, statusCode: 400 };
    }
    // JWT errors
    if (err.name === 'JsonWebTokenError') {
        const message = 'Invalid token';
        error = { ...error, message, statusCode: 401 };
    }
    if (err.name === 'TokenExpiredError') {
        const message = 'Token expired';
        error = { ...error, message, statusCode: 401 };
    }
    // Use the appropriate response function based on status code
    const statusCode = error.statusCode || 500;
    const message = error.message || 'Internal server error';
    const stack = process.env.NODE_ENV === 'development' ? err.stack : undefined;
    if (statusCode === 400) {
        res.status(400).json({
            success: false,
            message,
            error: stack
        });
    }
    else if (statusCode === 401) {
        res.status(401).json({
            success: false,
            message,
            error: stack
        });
    }
    else if (statusCode === 404) {
        res.status(404).json({
            success: false,
            message,
            error: stack
        });
    }
    else {
        (0, response_1.sendServerError)(res, message, stack);
    }
};
exports.errorHandler = errorHandler;
const notFound = (req, res, next) => {
    const error = new Error(`Not found - ${req.originalUrl}`);
    error.statusCode = 404;
    next(error);
};
exports.notFound = notFound;
