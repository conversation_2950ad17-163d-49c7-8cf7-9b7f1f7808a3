"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmailTemplateController = void 0;
const models_1 = require("../models");
const response_1 = require("../utils/response");
class EmailTemplateController {
    static async getEmailTemplate(req, res) {
        try {
            const { type } = req.params;
            const template = await models_1.EmailTemplate.findOne({ type, isActive: true });
            if (!template) {
                (0, response_1.sendNotFound)(res, 'Email template not found');
                return;
            }
            (0, response_1.sendSuccess)(res, 'Email template retrieved successfully', template);
        }
        catch (error) {
            console.error('Get email template error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async getAllEmailTemplates(req, res) {
        try {
            const { active } = req.query;
            const filter = {};
            if (active !== undefined) {
                filter.isActive = active === 'true';
            }
            const templates = await models_1.EmailTemplate.find(filter).sort({ type: 1 });
            (0, response_1.sendSuccess)(res, 'Email templates retrieved successfully', templates);
        }
        catch (error) {
            console.error('Get all email templates error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async updateEmailTemplate(req, res) {
        try {
            const { type } = req.params;
            const { subject, content, variables, isActive } = req.body;
            let template = await models_1.EmailTemplate.findOne({ type });
            if (!template) {
                // Create new template if it doesn't exist
                template = await models_1.EmailTemplate.create({
                    type,
                    subject,
                    content,
                    variables: variables || [],
                    isActive: isActive !== undefined ? isActive : true
                });
            }
            else {
                // Update existing template
                template.subject = subject || template.subject;
                template.content = content || template.content;
                template.variables = variables || template.variables;
                template.isActive = isActive !== undefined ? isActive : template.isActive;
                await template.save();
            }
            (0, response_1.sendSuccess)(res, 'Email template updated successfully', template);
        }
        catch (error) {
            console.error('Update email template error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async createEmailTemplate(req, res) {
        try {
            const templateData = req.body;
            const template = await models_1.EmailTemplate.create(templateData);
            (0, response_1.sendSuccess)(res, 'Email template created successfully', template, 201);
        }
        catch (error) {
            console.error('Create email template error:', error);
            if (error.code === 11000) {
                (0, response_1.sendError)(res, 'Email template for this type already exists');
            }
            else {
                (0, response_1.sendError)(res, error.message);
            }
        }
    }
    static async deleteEmailTemplate(req, res) {
        try {
            const { type } = req.params;
            const template = await models_1.EmailTemplate.findOneAndDelete({ type });
            if (!template) {
                (0, response_1.sendNotFound)(res, 'Email template not found');
                return;
            }
            (0, response_1.sendSuccess)(res, 'Email template deleted successfully');
        }
        catch (error) {
            console.error('Delete email template error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async previewEmailTemplate(req, res) {
        try {
            const { type } = req.params;
            const { variables } = req.body;
            const template = await models_1.EmailTemplate.findOne({ type, isActive: true });
            if (!template) {
                (0, response_1.sendNotFound)(res, 'Email template not found');
                return;
            }
            let previewContent = template.content;
            let previewSubject = template.subject;
            // Replace variables in content and subject
            if (variables && typeof variables === 'object') {
                Object.keys(variables).forEach(key => {
                    const placeholder = `{{${key}}}`;
                    previewContent = previewContent.replace(new RegExp(placeholder, 'g'), variables[key]);
                    previewSubject = previewSubject.replace(new RegExp(placeholder, 'g'), variables[key]);
                });
            }
            (0, response_1.sendSuccess)(res, 'Email template preview generated successfully', {
                type: template.type,
                subject: previewSubject,
                content: previewContent,
                variables: template.variables
            });
        }
        catch (error) {
            console.error('Preview email template error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
}
exports.EmailTemplateController = EmailTemplateController;
