"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContentController = void 0;
const models_1 = require("../models");
const response_1 = require("../utils/response");
class ContentController {
    static async getLegalContent(req, res) {
        try {
            const { type } = req.params;
            if (!['terms', 'privacy'].includes(type)) {
                (0, response_1.sendError)(res, 'Invalid content type');
                return;
            }
            let content = await models_1.Content.findOne({ type });
            if (!content) {
                // Create comprehensive default content
                const defaultContent = type === 'terms'
                    ? `
            <h1>Terms of Service</h1>
            <p><strong>Last updated:</strong> ${new Date().toLocaleDateString()}</p>

            <h2>1. Acceptance of Terms</h2>
            <p>By accessing and using MicroLocs services, you accept and agree to be bound by the terms and provision of this agreement.</p>

            <h2>2. Services</h2>
            <p>MicroLocs provides professional hair care services including microloc installation, maintenance, and related products.</p>

            <h2>3. Appointments</h2>
            <p>Appointments must be booked through our platform. Cancellations must be made at least 24 hours in advance to avoid fees.</p>

            <h2>4. Payment</h2>
            <p>Payment is due at the time of service. We accept cash, credit cards, and digital payments.</p>

            <h2>5. Liability</h2>
            <p>MicroLocs is not liable for any damages resulting from the use of our services beyond the cost of the service provided.</p>

            <h2>6. Contact Information</h2>
            <p>For questions about these Terms of Service, please contact <NAME_EMAIL></p>
          `
                    : `
            <h1>Privacy Policy</h1>
            <p><strong>Last updated:</strong> ${new Date().toLocaleDateString()}</p>

            <h2>1. Information We Collect</h2>
            <p>We collect information you provide directly to us, such as when you create an account, book appointments, or contact us.</p>

            <h2>2. How We Use Your Information</h2>
            <p>We use the information we collect to provide, maintain, and improve our services, process transactions, and communicate with you.</p>

            <h2>3. Information Sharing</h2>
            <p>We do not sell, trade, or otherwise transfer your personal information to third parties without your consent, except as described in this policy.</p>

            <h2>4. Data Security</h2>
            <p>We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.</p>

            <h2>5. Your Rights</h2>
            <p>You have the right to access, update, or delete your personal information. Contact <NAME_EMAIL> to exercise these rights.</p>

            <h2>6. Contact Us</h2>
            <p>If you have questions about this Privacy Policy, please contact <NAME_EMAIL></p>
          `;
                content = await models_1.Content.create({
                    type,
                    content: defaultContent
                });
            }
            (0, response_1.sendSuccess)(res, `${type} content retrieved successfully`, content);
        }
        catch (error) {
            console.error('Get legal content error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async updateLegalContent(req, res) {
        try {
            const { type } = req.params;
            const { content } = req.body;
            if (!['terms', 'privacy'].includes(type)) {
                (0, response_1.sendError)(res, 'Invalid content type');
                return;
            }
            let existingContent = await models_1.Content.findOne({ type });
            if (!existingContent) {
                existingContent = await models_1.Content.create({ type, content });
            }
            else {
                existingContent.content = content;
                await existingContent.save();
            }
            (0, response_1.sendSuccess)(res, `${type} content updated successfully`, existingContent);
        }
        catch (error) {
            console.error('Update legal content error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async getFAQContent(req, res) {
        try {
            const { category } = req.query;
            const filter = { isActive: true };
            if (category) {
                filter.category = category;
            }
            const faqs = await models_1.FAQ.find(filter).sort({ category: 1, order: 1 });
            // Group FAQs by category
            const groupedFAQs = faqs.reduce((acc, faq) => {
                if (!acc[faq.category]) {
                    acc[faq.category] = [];
                }
                acc[faq.category].push(faq);
                return acc;
            }, {});
            (0, response_1.sendSuccess)(res, 'FAQ content retrieved successfully', {
                faqs,
                groupedFAQs
            });
        }
        catch (error) {
            console.error('Get FAQ content error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async updateFAQContent(req, res) {
        try {
            const faqData = req.body;
            // Delete existing FAQs
            await models_1.FAQ.deleteMany({});
            // Create new FAQs
            const faqs = await models_1.FAQ.insertMany(faqData.map((faq, index) => ({
                ...faq,
                order: index
            })));
            (0, response_1.sendSuccess)(res, 'FAQ content updated successfully', faqs);
        }
        catch (error) {
            console.error('Update FAQ content error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async createFAQ(req, res) {
        try {
            const faqData = req.body;
            const faq = await models_1.FAQ.create(faqData);
            (0, response_1.sendSuccess)(res, 'FAQ created successfully', faq, 201);
        }
        catch (error) {
            console.error('Create FAQ error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async updateFAQ(req, res) {
        try {
            const { id } = req.params;
            const updateData = req.body;
            const faq = await models_1.FAQ.findByIdAndUpdate(id, updateData, { new: true, runValidators: true });
            if (!faq) {
                (0, response_1.sendNotFound)(res, 'FAQ not found');
                return;
            }
            (0, response_1.sendSuccess)(res, 'FAQ updated successfully', faq);
        }
        catch (error) {
            console.error('Update FAQ error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async deleteFAQ(req, res) {
        try {
            const { id } = req.params;
            const faq = await models_1.FAQ.findByIdAndDelete(id);
            if (!faq) {
                (0, response_1.sendNotFound)(res, 'FAQ not found');
                return;
            }
            (0, response_1.sendSuccess)(res, 'FAQ deleted successfully');
        }
        catch (error) {
            console.error('Delete FAQ error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
}
exports.ContentController = ContentController;
