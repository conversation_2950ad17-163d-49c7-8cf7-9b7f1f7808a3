export interface AddOnService {
  id: string;
  name: string;
  duration: number;
  price: number;
  description?: string;
}

export interface Service {
  id: string;
  name: string;
  price: string;
  description: string;
  category?: string;
}

export const addOnServices: AddOnService[] = [
  { 
    id: 'apple-cider', 
    name: 'Apple Cider Vinegar Detox', 
    duration: 30, 
    price: 55,
    description: 'Deep cleansing detox treatment with apple cider vinegar'
  },
  { 
    id: 'rose-water', 
    name: 'Rose water Detox', 
    duration: 30, 
    price: 55,
    description: 'Soothing detox treatment with rose water'
  },
  { 
    id: 'scalp-scrub', 
    name: 'Scalp scrub', 
    duration: 10, 
    price: 30,
    description: 'Exfoliating scalp treatment'
  },
  { 
    id: 'spa-wash', 
    name: 'Spa Hair Wash', 
    duration: 15, 
    price: 35,
    description: 'Luxurious spa-style hair washing experience'
  }
];

export const retieServices: Service[] = [
  // 4-5 weeks
  {
    id: 'retie-4-5-extra-small',
    name: '4-5 week Retightening - Extra Small',
    price: '300.00',
    description: '4-5 week maintenance for extra small locs',
    category: '4-5 weeks'
  },
  {
    id: 'retie-4-5-small',
    name: '4-5 week Retightening - Small',
    price: '200.00',
    description: '4-5 week maintenance for small locs',
    category: '4-5 weeks'
  },
  {
    id: 'retie-4-5-medium',
    name: '4-5 week Retightening - Medium',
    price: '150.00',
    description: '4-5 week maintenance for medium locs',
    category: '4-5 weeks'
  },
  // 6-7 weeks
  {
    id: 'retie-6-7-extra-small',
    name: '6-7 week Retightening - Extra Small',
    price: '350.00',
    description: '6-7 week maintenance for extra small locs',
    category: '6-7 weeks'
  },
  {
    id: 'retie-6-7-small',
    name: '6-7 week Retightening - Small',
    price: '250.00',
    description: '6-7 week maintenance for small locs',
    category: '6-7 weeks'
  },
  {
    id: 'retie-6-7-medium',
    name: '6-7 week Retightening - Medium',
    price: '200.00',
    description: '6-7 week maintenance for medium locs',
    category: '6-7 weeks'
  },
  // 8+ weeks
  {
    id: 'retie-8-extra-small',
    name: '8+ week Retightening - Extra Small',
    price: '400.00',
    description: '8+ week maintenance for extra small locs',
    category: '8+ weeks'
  },
  {
    id: 'retie-8-small',
    name: '8+ week Retightening - Small',
    price: '300.00',
    description: '8+ week maintenance for small locs',
    category: '8+ weeks'
  },
  {
    id: 'retie-8-medium',
    name: '8+ week Retightening - Medium',
    price: '250.00',
    description: '8+ week maintenance for medium locs',
    category: '8+ weeks'
  }
];

export const otherServices: Service[] = [
  {
    id: 'spa-hair-wash',
    name: 'Spa Hair Wash',
    price: '35.00',
    description: 'PLEASE NO EXTRA GUEST 🙃'
  },
  {
    id: 'consultation',
    name: 'Consultation',
    price: '30.00',
    description: 'Required prior to booking a Sisterlocks or Microlocks Establishment. At the consultation, client hair history will be discussed.'
  },
  {
    id: 'lock-repairs',
    name: 'Lock Repairs',
    price: '50.00',
    description: 'Professional lock repair service. Minimal 2hr Session. PLEASE NO EXTRA GUEST 🙃'
  }
];

// Filter add-ons for consultation (exclude add-ons for consultation appointments)
export const getAddOnsForService = (serviceId: string): AddOnService[] => {
  if (serviceId.includes('consultation')) {
    return []; // No add-ons for consultation
  }
  return addOnServices;
};
