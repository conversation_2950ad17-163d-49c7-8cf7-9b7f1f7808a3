"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentController = void 0;
const models_1 = require("../models");
const response_1 = require("../utils/response");
class PaymentController {
    static async getPaymentSettings(req, res) {
        try {
            let settings = await models_1.PaymentSettings.findOne();
            if (!settings) {
                // Create default settings if none exist
                settings = await models_1.PaymentSettings.create({});
            }
            (0, response_1.sendSuccess)(res, 'Payment settings retrieved successfully', settings);
        }
        catch (error) {
            console.error('Get payment settings error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async updatePaymentSettings(req, res) {
        try {
            const updateData = req.body;
            let settings = await models_1.PaymentSettings.findOne();
            if (!settings) {
                settings = await models_1.PaymentSettings.create(updateData);
            }
            else {
                Object.assign(settings, updateData);
                await settings.save();
            }
            (0, response_1.sendSuccess)(res, 'Payment settings updated successfully', settings);
        }
        catch (error) {
            console.error('Update payment settings error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
}
exports.PaymentController = PaymentController;
