"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ThemeSettings = void 0;
const mongoose_1 = __importStar(require("mongoose"));
const themeSettingsSchema = new mongoose_1.Schema({
    primaryColor: {
        type: String,
        required: true,
        default: '#007bff',
        match: /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/
    },
    secondaryColor: {
        type: String,
        required: true,
        default: '#6c757d',
        match: /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/
    },
    accentColor: {
        type: String,
        required: true,
        default: '#28a745',
        match: /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/
    },
    backgroundColor: {
        type: String,
        required: true,
        default: '#ffffff',
        match: /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/
    },
    textColor: {
        type: String,
        required: true,
        default: '#333333',
        match: /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/
    },
    fontFamily: {
        type: String,
        required: true,
        default: 'Inter, sans-serif',
        trim: true
    },
    fontSize: {
        type: String,
        required: true,
        default: '16px',
        trim: true
    },
    buttonStyle: {
        type: String,
        required: true,
        default: 'rounded',
        enum: ['rounded', 'square', 'pill']
    },
    cardStyle: {
        type: String,
        required: true,
        default: 'elevated',
        enum: ['elevated', 'outlined', 'filled']
    },
    borderRadius: {
        type: String,
        required: true,
        default: '8px',
        trim: true
    },
    spacing: {
        type: String,
        required: true,
        default: 'normal',
        enum: ['compact', 'normal', 'spacious']
    },
    customCSS: {
        type: String,
        default: '',
        trim: true
    },
    isActive: {
        type: Boolean,
        default: true
    }
}, {
    timestamps: true
});
exports.ThemeSettings = mongoose_1.default.model('ThemeSettings', themeSettingsSchema);
