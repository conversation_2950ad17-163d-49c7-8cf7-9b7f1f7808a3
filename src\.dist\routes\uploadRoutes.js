"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const controllers_1 = require("../controllers");
const auth_1 = require("../middleware/auth");
const router = (0, express_1.Router)();
// ===== UNIFIED CLOUDINARY UPLOAD ROUTES =====
// Check Cloudinary configuration
router.get('/cloudinary/config', controllers_1.UploadController.checkCloudinaryConfig);
// Single file upload with type parameter
// POST /api/upload/cloudinary/single/:uploadType
router.post('/cloudinary/single/:uploadType', auth_1.authenticate, controllers_1.UploadController.cloudinaryUploadSingle('file'), controllers_1.UploadController.uploadSingleFileToCloudinary);
// Multiple files upload with type parameter
// POST /api/upload/cloudinary/multiple/:uploadType
router.post('/cloudinary/multiple/:uploadType', auth_1.authenticate, controllers_1.UploadController.cloudinaryUploadMultiple('files', 10), controllers_1.UploadController.uploadMultipleFilesToCloudinary);
// DELETE /api/upload/cloudinary/image - Delete image from Cloudinary
router.delete('/cloudinary/image', auth_1.authenticate, controllers_1.UploadController.deleteImageFromCloudinary);
// GET /api/upload/cloudinary/optimize/:imageUrl - Get optimized image URL
router.get('/cloudinary/optimize/:imageUrl', controllers_1.UploadController.getOptimizedImage);
// Public payment proof upload (no authentication required)
// POST /api/upload/payment-proof
router.post('/payment-proof', controllers_1.UploadController.cloudinaryUploadSingle('file'), controllers_1.UploadController.uploadPaymentProof);
// ===== LEGACY LOCAL ROUTES (for backward compatibility) =====
// POST /api/upload/image
router.post('/image', auth_1.authenticate, controllers_1.UploadController.uploadMiddleware, controllers_1.UploadController.uploadImage);
// POST /api/upload/images
router.post('/images', auth_1.authenticate, controllers_1.UploadController.uploadMultipleImages);
// GET /api/upload/image/:filename
router.get('/image/:filename', controllers_1.UploadController.getImageInfo);
// DELETE /api/upload/image/:filename
router.delete('/image/:filename', auth_1.authenticate, controllers_1.UploadController.deleteImage);
exports.default = router;
