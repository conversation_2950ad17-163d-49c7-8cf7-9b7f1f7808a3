"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomerNoteController = void 0;
const models_1 = require("../models");
const response_1 = require("../utils/response");
class CustomerNoteController {
    static async createCustomerNote(req, res) {
        try {
            if (!req.user) {
                (0, response_1.sendError)(res, 'Authentication required', undefined, 401);
                return;
            }
            const { id } = req.params; // Customer ID
            const { content, isPrivate = true } = req.body;
            // Verify customer exists
            const customer = await models_1.User.findById(id);
            if (!customer) {
                (0, response_1.sendNotFound)(res, 'Customer not found');
                return;
            }
            const note = await models_1.CustomerNote.create({
                customer: id,
                content,
                createdBy: req.user._id,
                isPrivate
            });
            await note.populate('createdBy', 'name email');
            (0, response_1.sendCreated)(res, 'Customer note created successfully', note);
        }
        catch (error) {
            console.error('Create customer note error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async getCustomerNotes(req, res) {
        try {
            const { id } = req.params; // Customer ID
            const { page = 1, limit = 10 } = req.query;
            // Verify customer exists
            const customer = await models_1.User.findById(id);
            if (!customer) {
                (0, response_1.sendNotFound)(res, 'Customer not found');
                return;
            }
            const pageNum = Number(page);
            const limitNum = Number(limit);
            const skip = (pageNum - 1) * limitNum;
            const [notes, total] = await Promise.all([
                models_1.CustomerNote.find({ customer: id })
                    .populate('createdBy', 'name email')
                    .sort({ createdAt: -1 })
                    .skip(skip)
                    .limit(limitNum),
                models_1.CustomerNote.countDocuments({ customer: id })
            ]);
            const totalPages = Math.ceil(total / limitNum);
            (0, response_1.sendSuccess)(res, 'Customer notes retrieved successfully', {
                notes,
                pagination: {
                    currentPage: pageNum,
                    totalPages,
                    totalItems: total,
                    itemsPerPage: limitNum,
                    hasNextPage: pageNum < totalPages,
                    hasPrevPage: pageNum > 1
                }
            });
        }
        catch (error) {
            console.error('Get customer notes error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async updateCustomerNote(req, res) {
        try {
            if (!req.user) {
                (0, response_1.sendError)(res, 'Authentication required', undefined, 401);
                return;
            }
            const { noteId } = req.params;
            const { content, isPrivate } = req.body;
            const note = await models_1.CustomerNote.findById(noteId);
            if (!note) {
                (0, response_1.sendNotFound)(res, 'Note not found');
                return;
            }
            // Only allow the creator or admin to update the note
            if (note.createdBy.toString() !== req.user._id && req.user.role !== 'admin') {
                (0, response_1.sendError)(res, 'Not authorized to update this note', undefined, 403);
                return;
            }
            note.content = content || note.content;
            note.isPrivate = isPrivate !== undefined ? isPrivate : note.isPrivate;
            await note.save();
            await note.populate('createdBy', 'name email');
            (0, response_1.sendSuccess)(res, 'Customer note updated successfully', note);
        }
        catch (error) {
            console.error('Update customer note error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async deleteCustomerNote(req, res) {
        try {
            if (!req.user) {
                (0, response_1.sendError)(res, 'Authentication required', undefined, 401);
                return;
            }
            const { noteId } = req.params;
            const note = await models_1.CustomerNote.findById(noteId);
            if (!note) {
                (0, response_1.sendNotFound)(res, 'Note not found');
                return;
            }
            // Only allow the creator or admin to delete the note
            if (note.createdBy.toString() !== req.user._id && req.user.role !== 'admin') {
                (0, response_1.sendError)(res, 'Not authorized to delete this note', undefined, 403);
                return;
            }
            await models_1.CustomerNote.findByIdAndDelete(noteId);
            (0, response_1.sendSuccess)(res, 'Customer note deleted successfully');
        }
        catch (error) {
            console.error('Delete customer note error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
}
exports.CustomerNoteController = CustomerNoteController;
