"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PolicyController = void 0;
const models_1 = require("../models");
const response_1 = require("../utils/response");
class PolicyController {
    static async getPolicy(req, res) {
        try {
            const { type } = req.params;
            if (!['cancellation', 'refund', 'privacy', 'terms'].includes(type)) {
                (0, response_1.sendError)(res, 'Invalid policy type');
                return;
            }
            let policy = await models_1.Policy.findOne({ type, isActive: true });
            if (!policy) {
                // Create default policy if none exists
                const defaultPolicies = {
                    cancellation: {
                        title: 'Cancellation Policy',
                        content: 'Please review our cancellation policy below.',
                        rules: [
                            {
                                condition: 'Cancellation 24+ hours before appointment',
                                action: 'Full refund',
                                timeframe: '24 hours',
                                fee: 0
                            },
                            {
                                condition: 'Cancellation 2-24 hours before appointment',
                                action: 'Partial refund',
                                timeframe: '2-24 hours',
                                fee: 25
                            },
                            {
                                condition: 'Cancellation less than 2 hours before appointment',
                                action: 'No refund',
                                timeframe: 'Less than 2 hours',
                                fee: 100
                            }
                        ]
                    },
                    refund: {
                        title: 'Refund Policy',
                        content: 'Our refund policy ensures fair treatment for all customers.',
                        rules: [
                            {
                                condition: 'Service not satisfactory',
                                action: 'Full refund within 7 days',
                                timeframe: '7 days',
                                fee: 0
                            }
                        ]
                    },
                    privacy: {
                        title: 'Privacy Policy',
                        content: 'We respect your privacy and protect your personal information.',
                        rules: []
                    },
                    terms: {
                        title: 'Terms of Service',
                        content: 'By using our services, you agree to these terms.',
                        rules: []
                    }
                };
                policy = await models_1.Policy.create({
                    type,
                    ...defaultPolicies[type]
                });
            }
            (0, response_1.sendSuccess)(res, 'Policy retrieved successfully', policy);
        }
        catch (error) {
            console.error('Get policy error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async updatePolicy(req, res) {
        try {
            const { type } = req.params;
            const updateData = req.body;
            if (!['cancellation', 'refund', 'privacy', 'terms'].includes(type)) {
                (0, response_1.sendError)(res, 'Invalid policy type');
                return;
            }
            let policy = await models_1.Policy.findOne({ type });
            if (!policy) {
                policy = await models_1.Policy.create({ type, ...updateData });
            }
            else {
                Object.assign(policy, updateData);
                await policy.save();
            }
            (0, response_1.sendSuccess)(res, 'Policy updated successfully', policy);
        }
        catch (error) {
            console.error('Update policy error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async getAllPolicies(req, res) {
        try {
            const { active } = req.query;
            const filter = {};
            if (active !== undefined) {
                filter.isActive = active === 'true';
            }
            const policies = await models_1.Policy.find(filter).sort({ type: 1 });
            (0, response_1.sendSuccess)(res, 'Policies retrieved successfully', policies);
        }
        catch (error) {
            console.error('Get all policies error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
}
exports.PolicyController = PolicyController;
