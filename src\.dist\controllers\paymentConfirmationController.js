"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentConfirmationController = void 0;
const paymentConfirmationService_1 = require("../services/paymentConfirmationService");
const response_1 = require("../utils/response");
class PaymentConfirmationController {
    /**
     * Create a new payment confirmation
     */
    static async createPaymentConfirmation(req, res) {
        try {
            if (!req.user) {
                (0, response_1.sendError)(res, 'Authentication required', undefined, 401);
                return;
            }
            const { order, appointment, amount, paymentMethod, proofImage, notes } = req.body;
            // Debug logging
            console.log('Payment confirmation request:');
            console.log('- User from token:', req.user._id);
            console.log('- User type:', typeof req.user._id);
            console.log('- Appointment ID:', appointment);
            console.log('- Request body:', { order, appointment, amount, paymentMethod, notes: notes ? 'present' : 'none', proofImage: proofImage ? 'present' : 'none' });
            const confirmation = await paymentConfirmationService_1.PaymentConfirmationService.createPaymentConfirmation({
                user: req.user._id,
                order,
                appointment,
                amount,
                paymentMethod,
                proofImage,
                notes
            });
            (0, response_1.sendCreated)(res, 'Payment confirmation submitted successfully', confirmation);
        }
        catch (error) {
            console.error('Create payment confirmation error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    /**
     * Get user's own payment confirmations
     */
    static async getUserPaymentConfirmations(req, res) {
        try {
            if (!req.user) {
                (0, response_1.sendError)(res, 'Authentication required', undefined, 401);
                return;
            }
            const { page, limit } = req.query;
            const result = await paymentConfirmationService_1.PaymentConfirmationService.getUserPaymentConfirmations(req.user._id, page ? parseInt(page) : undefined, limit ? parseInt(limit) : undefined);
            (0, response_1.sendSuccess)(res, 'Payment confirmations retrieved successfully', result);
        }
        catch (error) {
            console.error('Get user payment confirmations error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    /**
     * Get payment confirmation by ID
     */
    static async getPaymentConfirmationById(req, res) {
        try {
            if (!req.user) {
                (0, response_1.sendError)(res, 'Authentication required', undefined, 401);
                return;
            }
            const { id } = req.params;
            const confirmation = await paymentConfirmationService_1.PaymentConfirmationService.getPaymentConfirmationById(id);
            if (!confirmation) {
                (0, response_1.sendError)(res, 'Payment confirmation not found', undefined, 404);
                return;
            }
            // Users can only view their own confirmations, admins can view all
            if (req.user.role !== 'admin' && confirmation.user.toString() !== req.user._id) {
                (0, response_1.sendError)(res, 'Access denied', undefined, 403);
                return;
            }
            (0, response_1.sendSuccess)(res, 'Payment confirmation retrieved successfully', confirmation);
        }
        catch (error) {
            console.error('Get payment confirmation error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    /**
     * Update user's own payment confirmation
     */
    static async updatePaymentConfirmation(req, res) {
        try {
            if (!req.user) {
                (0, response_1.sendError)(res, 'Authentication required', undefined, 401);
                return;
            }
            const { id } = req.params;
            const { amount, paymentMethod, notes, proofImage } = req.body;
            const confirmation = await paymentConfirmationService_1.PaymentConfirmationService.updatePaymentConfirmation(id, req.user._id, { amount, paymentMethod, notes, proofImage });
            if (!confirmation) {
                (0, response_1.sendError)(res, 'Payment confirmation not found or cannot be edited', undefined, 404);
                return;
            }
            (0, response_1.sendSuccess)(res, 'Payment confirmation updated successfully', confirmation);
        }
        catch (error) {
            console.error('Update payment confirmation error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    /**
     * Delete user's own payment confirmation
     */
    static async deletePaymentConfirmation(req, res) {
        try {
            if (!req.user) {
                (0, response_1.sendError)(res, 'Authentication required', undefined, 401);
                return;
            }
            const { id } = req.params;
            const deleted = await paymentConfirmationService_1.PaymentConfirmationService.deletePaymentConfirmation(id, req.user._id);
            if (!deleted) {
                (0, response_1.sendError)(res, 'Payment confirmation not found', undefined, 404);
                return;
            }
            (0, response_1.sendSuccess)(res, 'Payment confirmation deleted successfully');
        }
        catch (error) {
            console.error('Delete payment confirmation error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    /**
     * Admin: Get all payment confirmations
     */
    static async getAllPaymentConfirmations(req, res) {
        try {
            if (!req.user || req.user.role !== 'admin') {
                (0, response_1.sendError)(res, 'Admin access required', undefined, 403);
                return;
            }
            const { status, page, limit, search } = req.query;
            const result = await paymentConfirmationService_1.PaymentConfirmationService.getAllPaymentConfirmations({
                status: status,
                page: page ? parseInt(page) : undefined,
                limit: limit ? parseInt(limit) : undefined,
                search: search
            });
            (0, response_1.sendSuccess)(res, 'All payment confirmations retrieved successfully', result);
        }
        catch (error) {
            console.error('Get all payment confirmations error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    /**
     * Admin: Update payment confirmation status
     */
    static async updatePaymentConfirmationStatus(req, res) {
        try {
            if (!req.user || req.user.role !== 'admin') {
                (0, response_1.sendError)(res, 'Admin access required', undefined, 403);
                return;
            }
            const { id } = req.params;
            const { status, rejectionReason } = req.body;
            if (!['verified', 'rejected'].includes(status)) {
                (0, response_1.sendError)(res, 'Invalid status. Must be "verified" or "rejected"', undefined, 400);
                return;
            }
            const confirmation = await paymentConfirmationService_1.PaymentConfirmationService.updatePaymentConfirmationStatus(id, status, req.user._id, rejectionReason);
            if (!confirmation) {
                (0, response_1.sendError)(res, 'Payment confirmation not found', undefined, 404);
                return;
            }
            (0, response_1.sendSuccess)(res, `Payment confirmation ${status} successfully`, confirmation);
        }
        catch (error) {
            console.error('Update payment confirmation status error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    /**
     * Admin: Delete any payment confirmation
     */
    static async adminDeletePaymentConfirmation(req, res) {
        try {
            if (!req.user || req.user.role !== 'admin') {
                (0, response_1.sendError)(res, 'Admin access required', undefined, 403);
                return;
            }
            const { id } = req.params;
            const deleted = await paymentConfirmationService_1.PaymentConfirmationService.deletePaymentConfirmation(id);
            if (!deleted) {
                (0, response_1.sendError)(res, 'Payment confirmation not found', undefined, 404);
                return;
            }
            (0, response_1.sendSuccess)(res, 'Payment confirmation deleted successfully');
        }
        catch (error) {
            console.error('Admin delete payment confirmation error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    /**
     * Admin: Get payment confirmation statistics
     */
    static async getPaymentConfirmationStats(req, res) {
        try {
            if (!req.user || req.user.role !== 'admin') {
                (0, response_1.sendError)(res, 'Admin access required', undefined, 403);
                return;
            }
            const stats = await paymentConfirmationService_1.PaymentConfirmationService.getPaymentConfirmationStats();
            (0, response_1.sendSuccess)(res, 'Payment confirmation statistics retrieved successfully', stats);
        }
        catch (error) {
            console.error('Get payment confirmation stats error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
}
exports.PaymentConfirmationController = PaymentConfirmationController;
