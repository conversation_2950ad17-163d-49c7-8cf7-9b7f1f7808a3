"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SiteSettingsController = void 0;
const models_1 = require("../models");
const response_1 = require("../utils/response");
class SiteSettingsController {
    static async getSiteSettings(req, res) {
        try {
            let settings = await models_1.SiteSettings.findOne();
            if (!settings) {
                // Create default settings if none exist
                settings = await models_1.SiteSettings.create({});
            }
            (0, response_1.sendSuccess)(res, 'Site settings retrieved successfully', settings);
        }
        catch (error) {
            console.error('Get site settings error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async updateSiteSettings(req, res) {
        try {
            const updateData = req.body;
            let settings = await models_1.SiteSettings.findOne();
            if (!settings) {
                settings = await models_1.SiteSettings.create(updateData);
            }
            else {
                // Deep merge the update data
                Object.keys(updateData).forEach(key => {
                    if (typeof updateData[key] === 'object' && updateData[key] !== null) {
                        settings[key] = { ...settings[key], ...updateData[key] };
                    }
                    else {
                        settings[key] = updateData[key];
                    }
                });
                await settings.save();
            }
            (0, response_1.sendSuccess)(res, 'Site settings updated successfully', settings);
        }
        catch (error) {
            console.error('Update site settings error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async getPublicSettings(req, res) {
        try {
            const settings = await models_1.SiteSettings.findOne();
            if (!settings) {
                (0, response_1.sendSuccess)(res, 'Public settings retrieved successfully', {
                    general: {
                        siteName: 'MicroLocs',
                        siteDescription: 'Professional hair care services',
                        contactEmail: '<EMAIL>',
                        contactPhone: '',
                        address: '',
                        timezone: 'America/New_York',
                        currency: 'USD',
                        language: 'en'
                    },
                    features: {
                        enableAppointments: true,
                        enableEcommerce: true,
                        enableReviews: true,
                        enableLoyaltyProgram: false,
                        enableGiftCards: false,
                        enableWaitlist: false,
                        enableReferrals: false
                    }
                });
                return;
            }
            // Return only public settings (exclude sensitive data)
            const publicSettings = {
                general: settings.general,
                features: settings.features,
                maintenance: {
                    isMaintenanceMode: settings.maintenance.isMaintenanceMode,
                    maintenanceMessage: settings.maintenance.maintenanceMessage
                }
            };
            (0, response_1.sendSuccess)(res, 'Public settings retrieved successfully', publicSettings);
        }
        catch (error) {
            console.error('Get public settings error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
}
exports.SiteSettingsController = SiteSettingsController;
