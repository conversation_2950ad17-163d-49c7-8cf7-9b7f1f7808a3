import { useState } from 'react'
import { FiMenu, FiX, FiShoppingCart, FiUser, FiArrowLeft } from 'react-icons/fi'
import { useBranding } from '../../contexts/BrandingContext'
import { getBrandingColor } from '../../utils/constants'

const Navbar = ({ currentView, onNavigate, user, onLogout, goBack, canGoBack }) => {
  const { branding, isLoading } = useBranding()
  const [isOpen, setIsOpen] = useState(false)

  const isActive = (view) => currentView === view

  // Show loading navbar if branding is not available
  if (isLoading || !branding) {
    return (
      <nav className="bg-white shadow-lg sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="animate-pulse">
              <div className="h-6 bg-gray-200 rounded w-32"></div>
            </div>
            <div className="animate-pulse flex space-x-4">
              <div className="h-4 bg-gray-200 rounded w-16"></div>
              <div className="h-4 bg-gray-200 rounded w-16"></div>
              <div className="h-4 bg-gray-200 rounded w-16"></div>
            </div>
          </div>
        </div>
      </nav>
    )
  }

  const navLinks = [
    { name: branding?.navigation?.home || 'Home', view: 'home' },
    { name: branding?.navigation?.services || 'Services', view: 'services' },
    { name: branding?.navigation?.consultation || 'Consultation', view: 'consultation' },
    { name: branding?.navigation?.shop || 'Shop', view: 'shop' },
    { name: branding?.navigation?.contact || 'Contact', view: 'contact' },
  ]

  return (
    <nav className="bg-white shadow-lg sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Back Button & Logo */}
          <div className="flex items-center space-x-3">
            {canGoBack && currentView !== 'home' && (
              <button
                onClick={goBack}
                className="p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200 cursor-pointer"
                title="Go back"
              >
                <FiArrowLeft className="w-5 h-5" />
              </button>
            )}
            <button
              onClick={() => onNavigate('home')}
              className="flex items-center space-x-2 hover:opacity-80 transition-opacity duration-200"
            >
              <div className="text-2xl font-bold" style={{ color: getBrandingColor(branding, 'secondary') }}>
                {branding?.businessName || ''}
              </div>
              {branding.tagline && (
                <div className="text-sm text-gray-600 hidden sm:block">
                  {branding.tagline}
                </div>
              )}
            </button>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            {navLinks.map((link) => (
              <button
                key={link.name}
                onClick={() => onNavigate(link.view)}
                className={`px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 ${
                  isActive(link.view)
                    ? 'bg-yellow-50'
                    : 'text-gray-700 hover:bg-yellow-50'
                }`}
                style={isActive(link.view) ? { color: getBrandingColor(branding, 'secondary') } : {}}
              >
                {link.name}
              </button>
            ))}
          </div>

          {/* Desktop Auth & Cart */}
          <div className="hidden md:flex items-center space-x-4">
            <button
              onClick={() => onNavigate('cart')}
              className="p-2 text-gray-700 transition-colors duration-200"
              onMouseEnter={(e) => e.target.style.color = getBrandingColor(branding, 'secondary')}
              onMouseLeave={(e) => e.target.style.color = ''}
            >
              <FiShoppingCart className="w-5 h-5" />
            </button>
            {user ? (
              <>
                <button
                  onClick={() => onNavigate(user === 'admin' ? 'admin-dashboard' : 'user-dashboard')}
                  className="flex items-center px-4 py-2 text-sm font-medium text-gray-700 transition-colors duration-200"
                  onMouseEnter={(e) => e.target.style.color = getBrandingColor(branding, 'secondary')}
                  onMouseLeave={(e) => e.target.style.color = ''}
                >
                  <FiUser className="w-4 h-4 mr-2" />
                  Dashboard
                </button>
                <button
                  onClick={() => {
                    onLogout()
                    onNavigate('home')
                  }}
                  className="px-4 py-2 text-sm font-medium text-gray-700 transition-colors duration-200"
                  onMouseEnter={(e) => e.target.style.color = getBrandingColor(branding, 'secondary')}
                  onMouseLeave={(e) => e.target.style.color = ''}
                >
                  Logout
                </button>
              </>
            ) : (
              <>
                <button
                  onClick={() => onNavigate('login')}
                  className="px-4 py-2 text-sm font-medium text-gray-700 transition-colors duration-200"
                  onMouseEnter={(e) => e.target.style.color = getBrandingColor(branding, 'secondary')}
                  onMouseLeave={(e) => e.target.style.color = ''}
                >
                  Login
                </button>
                <button
                  onClick={() => onNavigate('signup')}
                  className="px-4 py-2 text-sm font-medium text-white rounded-md transition-colors duration-200"
                  style={{ backgroundColor: getBrandingColor(branding, 'secondary') }}
                  onMouseEnter={(e) => e.target.style.backgroundColor = getBrandingColor(branding, 'accent')}
                  onMouseLeave={(e) => e.target.style.backgroundColor = getBrandingColor(branding, 'secondary')}
                >
                  Sign Up
                </button>
              </>
            )}
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={() => setIsOpen(!isOpen)}
              className="p-2 text-gray-700 transition-colors duration-200"
              onMouseEnter={(e) => e.target.style.color = getBrandingColor(branding, 'secondary')}
              onMouseLeave={(e) => e.target.style.color = ''}
            >
              {isOpen ? <FiX className="w-6 h-6" /> : <FiMenu className="w-6 h-6" />}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t">
              {navLinks.map((link) => (
                <button
                  key={link.name}
                  onClick={() => {
                    onNavigate(link.view)
                    setIsOpen(false)
                  }}
                  className={`block w-full text-left px-3 py-2 rounded-md text-base font-medium transition-colors duration-200 ${
                    isActive(link.view)
                      ? 'bg-yellow-50'
                      : 'text-gray-700 hover:bg-yellow-50'
                  }`}
                  style={isActive(link.view) ? { color: getBrandingColor(branding, 'secondary') } : {}}
                >
                  {link.name}
                </button>
              ))}
              <div className="border-t pt-3 mt-3">
                <button
                  onClick={() => {
                    onNavigate('cart')
                    setIsOpen(false)
                  }}
                  className="flex items-center w-full px-3 py-2 rounded-md text-base font-medium text-gray-700 transition-colors duration-200 cursor-pointer"
                  onMouseEnter={(e) => {
                    e.target.style.color = getBrandingColor(branding, 'secondary')
                    e.target.style.backgroundColor = `${getBrandingColor(branding, 'secondary')}10`
                  }}
                  onMouseLeave={(e) => {
                    e.target.style.color = ''
                    e.target.style.backgroundColor = ''
                  }}
                >
                  <FiShoppingCart className="w-5 h-5 mr-2" />
                  Cart
                </button>
                {user ? (
                  <>
                    <button
                      onClick={() => {
                        onNavigate(user === 'admin' ? 'admin-dashboard' : 'user-dashboard')
                        setIsOpen(false)
                      }}
                      className="flex items-center w-full px-3 py-2 rounded-md text-base font-medium text-gray-700 transition-colors duration-200 cursor-pointer"
                      onMouseEnter={(e) => {
                        e.target.style.color = getBrandingColor(branding, 'secondary')
                        e.target.style.backgroundColor = `${getBrandingColor(branding, 'secondary')}10`
                      }}
                      onMouseLeave={(e) => {
                        e.target.style.color = ''
                        e.target.style.backgroundColor = ''
                      }}
                    >
                      <FiUser className="w-5 h-5 mr-2" />
                      Dashboard
                    </button>
                    <button
                      onClick={() => {
                        onLogout()
                        onNavigate('home')
                        setIsOpen(false)
                      }}
                      className="block w-full text-left px-3 py-2 rounded-md text-base font-medium text-gray-700 transition-colors duration-200 cursor-pointer"
                      onMouseEnter={(e) => {
                        e.target.style.color = getBrandingColor(branding, 'secondary')
                        e.target.style.backgroundColor = `${getBrandingColor(branding, 'secondary')}10`
                      }}
                      onMouseLeave={(e) => {
                        e.target.style.color = ''
                        e.target.style.backgroundColor = ''
                      }}
                    >
                      Logout
                    </button>
                  </>
                ) : (
                  <>
                    <button
                      onClick={() => {
                        onNavigate('login')
                        setIsOpen(false)
                      }}
                      className="block w-full text-left px-3 py-2 rounded-md text-base font-medium text-gray-700 transition-colors duration-200 cursor-pointer"
                      onMouseEnter={(e) => {
                        e.target.style.color = getBrandingColor(branding, 'secondary')
                        e.target.style.backgroundColor = `${getBrandingColor(branding, 'secondary')}10`
                      }}
                      onMouseLeave={(e) => {
                        e.target.style.color = ''
                        e.target.style.backgroundColor = ''
                      }}
                    >
                      Login
                    </button>
                    <button
                      onClick={() => {
                        onNavigate('signup')
                        setIsOpen(false)
                      }}
                      className="block w-full text-left px-3 py-2 mt-2 rounded-md text-base font-medium text-white transition-colors duration-200"
                      style={{ backgroundColor: getBrandingColor(branding, 'secondary') }}
                      onMouseEnter={(e) => e.target.style.backgroundColor = getBrandingColor(branding, 'accent')}
                      onMouseLeave={(e) => e.target.style.backgroundColor = getBrandingColor(branding, 'secondary')}
                    >
                      Sign Up
                    </button>
                  </>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </nav>
  )
}

export default Navbar
