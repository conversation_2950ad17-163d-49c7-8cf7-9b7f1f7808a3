"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const paymentConfirmationController_1 = require("../controllers/paymentConfirmationController");
const auth_1 = require("../middleware/auth");
const validation_1 = require("../middleware/validation");
const validation_2 = require("../utils/validation");
const router = (0, express_1.Router)();
// User routes (authenticated)
// POST /api/payment-confirmations - Create new payment confirmation
router.post('/', auth_1.authenticate, (0, validation_1.validate)(validation_2.createPaymentConfirmationValidation), paymentConfirmationController_1.PaymentConfirmationController.createPaymentConfirmation);
// GET /api/payment-confirmations/my - Get user's own payment confirmations
router.get('/my', auth_1.authenticate, (0, validation_1.validate)(validation_2.paginationValidation), paymentConfirmationController_1.PaymentConfirmationController.getUserPaymentConfirmations);
// GET /api/payment-confirmations/:id - Get payment confirmation by ID
router.get('/:id', auth_1.authenticate, (0, validation_1.validate)((0, validation_2.mongoIdValidation)()), paymentConfirmationController_1.PaymentConfirmationController.getPaymentConfirmationById);
// PUT /api/payment-confirmations/:id - Update user's own payment confirmation
router.put('/:id', auth_1.authenticate, (0, validation_1.validate)([...(0, validation_2.mongoIdValidation)(), ...validation_2.updatePaymentConfirmationValidation]), paymentConfirmationController_1.PaymentConfirmationController.updatePaymentConfirmation);
// DELETE /api/payment-confirmations/:id - Delete user's own payment confirmation
router.delete('/:id', auth_1.authenticate, (0, validation_1.validate)((0, validation_2.mongoIdValidation)()), paymentConfirmationController_1.PaymentConfirmationController.deletePaymentConfirmation);
// Admin routes
// GET /api/payment-confirmations/admin/all - Get all payment confirmations for admin
router.get('/admin/all', auth_1.authenticate, (0, validation_1.validate)(validation_2.paginationValidation), paymentConfirmationController_1.PaymentConfirmationController.getAllPaymentConfirmations);
// GET /api/payment-confirmations/admin/stats - Get payment confirmation statistics
router.get('/admin/stats', auth_1.authenticate, paymentConfirmationController_1.PaymentConfirmationController.getPaymentConfirmationStats);
// PUT /api/payment-confirmations/admin/:id/status - Update payment confirmation status
router.put('/admin/:id/status', auth_1.authenticate, (0, validation_1.validate)([...(0, validation_2.mongoIdValidation)(), ...validation_2.paymentConfirmationStatusValidation]), paymentConfirmationController_1.PaymentConfirmationController.updatePaymentConfirmationStatus);
// DELETE /api/payment-confirmations/admin/:id - Admin delete any payment confirmation
router.delete('/admin/:id', auth_1.authenticate, (0, validation_1.validate)((0, validation_2.mongoIdValidation)()), paymentConfirmationController_1.PaymentConfirmationController.adminDeletePaymentConfirmation);
exports.default = router;
