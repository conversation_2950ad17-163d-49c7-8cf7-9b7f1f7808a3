"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.GiftCard = void 0;
const mongoose_1 = __importStar(require("mongoose"));
const giftCardSchema = new mongoose_1.Schema({
    code: {
        type: String,
        required: true,
        uppercase: true,
        trim: true
    },
    amount: {
        type: Number,
        required: true,
        min: 10,
        max: 1000
    },
    balance: {
        type: Number,
        required: true,
        min: 0
    },
    recipientName: {
        type: String,
        required: true,
        trim: true,
        maxlength: 100
    },
    recipientEmail: {
        type: String,
        required: true,
        trim: true,
        lowercase: true
    },
    message: {
        type: String,
        trim: true,
        maxlength: 500,
        default: ''
    },
    senderName: {
        type: String,
        required: true,
        trim: true,
        maxlength: 100
    },
    purchasedBy: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    isActive: {
        type: Boolean,
        default: true
    },
    expiryDate: {
        type: Date,
        required: true
    }
}, {
    timestamps: true
});
// Index for better query performance
giftCardSchema.index({ code: 1 }, { unique: true });
giftCardSchema.index({ recipientEmail: 1 });
giftCardSchema.index({ purchasedBy: 1 });
giftCardSchema.index({ isActive: 1 });
giftCardSchema.index({ expiryDate: 1 });
// Generate gift card code before saving
giftCardSchema.pre('save', function (next) {
    if (!this.code) {
        this.code = 'GC' + Date.now().toString() + Math.random().toString(36).substring(2, 8).toUpperCase();
    }
    if (!this.balance) {
        this.balance = this.amount;
    }
    next();
});
exports.GiftCard = mongoose_1.default.model('GiftCard', giftCardSchema);
