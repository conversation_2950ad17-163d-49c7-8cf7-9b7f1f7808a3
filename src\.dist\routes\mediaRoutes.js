"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const mediaController_1 = require("../controllers/mediaController");
const auth_1 = require("../middleware/auth");
const upload_1 = require("../middleware/upload");
const router = (0, express_1.Router)();
// Public routes
router.get('/library', mediaController_1.MediaController.getMediaLibrary);
router.get('/stats', mediaController_1.MediaController.getMediaStats);
router.get('/tags', mediaController_1.MediaController.getAllTags);
router.get('/:id', mediaController_1.MediaController.getMediaById);
// Protected routes (require authentication)
router.post('/upload', auth_1.authenticate, upload_1.upload.single('file'), mediaController_1.MediaController.uploadMedia);
router.put('/:id', auth_1.authenticate, mediaController_1.MediaController.updateMedia);
router.delete('/:id', auth_1.authenticate, mediaController_1.MediaController.deleteMedia);
router.post('/bulk-delete', auth_1.authenticate, mediaController_1.MediaController.bulkDeleteMedia);
// Usage tracking routes
router.post('/:mediaId/track-usage', auth_1.authenticate, mediaController_1.MediaController.trackMediaUsage);
router.delete('/:mediaId/remove-usage', auth_1.authenticate, mediaController_1.MediaController.removeMediaUsage);
exports.default = router;
