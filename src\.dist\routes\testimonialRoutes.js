"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const controllers_1 = require("../controllers");
const auth_1 = require("../middleware/auth");
const validation_1 = require("../middleware/validation");
const validation_2 = require("../utils/validation");
const router = (0, express_1.Router)();
// GET /api/testimonials
router.get('/', (0, validation_1.validate)(validation_2.paginationValidation), controllers_1.TestimonialController.getTestimonials);
// GET /api/testimonials/:id
router.get('/:id', (0, validation_1.validate)((0, validation_2.mongoIdValidation)()), controllers_1.TestimonialController.getTestimonialById);
// Admin routes
// POST /api/testimonials (admin only)
router.post('/', auth_1.authenticate, (0, auth_1.authorize)('admin'), controllers_1.TestimonialController.createTestimonial);
// PUT /api/testimonials/:id (admin only)
router.put('/:id', auth_1.authenticate, (0, auth_1.authorize)('admin'), (0, validation_1.validate)((0, validation_2.mongoIdValidation)()), controllers_1.TestimonialController.updateTestimonial);
// DELETE /api/testimonials/:id (admin only)
router.delete('/:id', auth_1.authenticate, (0, auth_1.authorize)('admin'), (0, validation_1.validate)((0, validation_2.mongoIdValidation)()), controllers_1.TestimonialController.deleteTestimonial);
exports.default = router;
