"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentConfirmationService = void 0;
const PaymentConfirmation_1 = require("../models/PaymentConfirmation");
const Order_1 = require("../models/Order");
const Appointment_1 = require("../models/Appointment");
const cloudinaryService_1 = require("../services/cloudinaryService");
class PaymentConfirmationService {
    /**
     * Create a new payment confirmation
     */
    static async createPaymentConfirmation(data) {
        // Verify order or appointment exists
        if (data.order) {
            const order = await Order_1.Order.findById(data.order);
            if (!order) {
                throw new Error('Order not found');
            }
            if (order.user.toString() !== data.user) {
                throw new Error('Order does not belong to this user');
            }
        }
        else if (data.appointment) {
            const appointment = await Appointment_1.Appointment.findById(data.appointment);
            if (!appointment) {
                throw new Error('Appointment not found');
            }
            // Debug logging to identify the issue
            console.log('Payment confirmation validation:');
            console.log('- Appointment ID:', data.appointment);
            console.log('- Appointment user (from DB):', appointment.user.toString());
            console.log('- Appointment user type:', typeof appointment.user);
            console.log('- Request user (from token):', data.user);
            console.log('- Request user type:', typeof data.user);
            console.log('- User comparison result:', appointment.user.toString() === data.user.toString());
            // Ensure both values are strings for comparison
            const appointmentUserId = appointment.user.toString();
            const requestUserId = data.user.toString();
            if (appointmentUserId !== requestUserId) {
                throw new Error(`Appointment does not belong to this user. Appointment user: ${appointmentUserId}, Request user: ${requestUserId}`);
            }
        }
        // Upload image to Cloudinary
        let imageUrl;
        try {
            imageUrl = await (0, cloudinaryService_1.uploadToCloudinary)(data.proofImage, 'payment-confirmations');
        }
        catch (error) {
            throw new Error('Failed to upload payment proof image');
        }
        const paymentConfirmation = new PaymentConfirmation_1.PaymentConfirmation({
            user: data.user,
            order: data.order,
            appointment: data.appointment,
            amount: data.amount,
            paymentMethod: data.paymentMethod,
            proofImage: imageUrl,
            notes: data.notes,
            status: 'pending'
        });
        return await paymentConfirmation.save();
    }
    /**
     * Get payment confirmations for a user
     */
    static async getUserPaymentConfirmations(userId, page = 1, limit = 10) {
        const skip = (page - 1) * limit;
        const [confirmations, total] = await Promise.all([
            PaymentConfirmation_1.PaymentConfirmation.find({ user: userId })
                .populate('order', 'orderNumber total status')
                .populate('appointment', 'date time service status')
                .sort({ createdAt: -1 })
                .skip(skip)
                .limit(limit),
            PaymentConfirmation_1.PaymentConfirmation.countDocuments({ user: userId })
        ]);
        return {
            confirmations,
            total,
            page,
            totalPages: Math.ceil(total / limit),
            hasMore: page * limit < total
        };
    }
    /**
     * Get all payment confirmations for admin
     */
    static async getAllPaymentConfirmations(params) {
        const { status, page = 1, limit = 20, search } = params;
        const query = {};
        if (status && status !== 'all')
            query.status = status;
        // Add search functionality
        if (search) {
            query.$or = [
                { paymentMethod: { $regex: search, $options: 'i' } },
                { notes: { $regex: search, $options: 'i' } }
            ];
        }
        const skip = (page - 1) * limit;
        const [confirmations, total] = await Promise.all([
            PaymentConfirmation_1.PaymentConfirmation.find(query)
                .populate('user', 'firstName lastName name email')
                .populate('order', 'orderNumber total status')
                .populate('appointment', 'date time service status')
                .populate('verifiedBy', 'firstName lastName name')
                .sort({ createdAt: -1 })
                .skip(skip)
                .limit(limit),
            PaymentConfirmation_1.PaymentConfirmation.countDocuments(query)
        ]);
        return {
            confirmations,
            total,
            page,
            totalPages: Math.ceil(total / limit),
            hasMore: page * limit < total
        };
    }
    /**
     * Update payment confirmation status (admin only)
     */
    static async updatePaymentConfirmationStatus(confirmationId, status, verifiedBy, rejectionReason) {
        const updateData = {
            status,
            verifiedBy,
            verifiedAt: new Date()
        };
        if (status === 'rejected' && rejectionReason) {
            updateData.rejectionReason = rejectionReason;
        }
        const confirmation = await PaymentConfirmation_1.PaymentConfirmation.findByIdAndUpdate(confirmationId, updateData, { new: true }).populate('user', 'firstName lastName name email')
            .populate('order', 'orderNumber total status')
            .populate('appointment', 'date time service status')
            .populate('verifiedBy', 'firstName lastName name');
        // If verified, update order/appointment status
        if (confirmation && status === 'verified') {
            if (confirmation.order) {
                await Order_1.Order.findByIdAndUpdate(confirmation.order, {
                    status: 'paid',
                    paymentStatus: 'completed'
                });
            }
            else if (confirmation.appointment) {
                await Appointment_1.Appointment.findByIdAndUpdate(confirmation.appointment, {
                    status: 'confirmed',
                    paymentStatus: 'paid'
                });
                console.log(`Appointment ${confirmation.appointment} status updated to 'confirmed' after payment verification`);
            }
        }
        return confirmation;
    }
    /**
     * Delete a payment confirmation
     */
    static async deletePaymentConfirmation(confirmationId, userId) {
        const confirmation = await PaymentConfirmation_1.PaymentConfirmation.findById(confirmationId);
        if (!confirmation) {
            return false;
        }
        // Users can only delete their own confirmations
        if (userId && confirmation.user.toString() !== userId) {
            throw new Error('You can only delete your own payment confirmations');
        }
        // Delete image from Cloudinary
        try {
            if (confirmation.proofImage) {
                await (0, cloudinaryService_1.deleteFromCloudinary)(confirmation.proofImage);
            }
        }
        catch (error) {
            console.error('Failed to delete image from Cloudinary:', error);
            // Continue with deletion even if image deletion fails
        }
        const result = await PaymentConfirmation_1.PaymentConfirmation.deleteOne({ _id: confirmationId });
        return result.deletedCount > 0;
    }
    /**
     * Get payment confirmation by ID
     */
    static async getPaymentConfirmationById(confirmationId) {
        return await PaymentConfirmation_1.PaymentConfirmation.findById(confirmationId)
            .populate('user', 'firstName lastName name email')
            .populate('order', 'orderNumber total status items')
            .populate('appointment', 'date time service status')
            .populate('verifiedBy', 'firstName lastName name');
    }
    /**
     * Update payment confirmation (user can edit their own)
     */
    static async updatePaymentConfirmation(confirmationId, userId, updateData) {
        const confirmation = await PaymentConfirmation_1.PaymentConfirmation.findOne({
            _id: confirmationId,
            user: userId,
            status: 'pending' // Only allow editing pending confirmations
        });
        if (!confirmation) {
            throw new Error('Payment confirmation not found or cannot be edited');
        }
        // If updating image, upload new one and delete old one
        if (updateData.proofImage) {
            try {
                // Upload new image
                const newImageUrl = await (0, cloudinaryService_1.uploadToCloudinary)(updateData.proofImage, 'payment-confirmations');
                // Delete old image
                if (confirmation.proofImage) {
                    await (0, cloudinaryService_1.deleteFromCloudinary)(confirmation.proofImage);
                }
                updateData.proofImage = newImageUrl;
            }
            catch (error) {
                throw new Error('Failed to update payment proof image');
            }
        }
        return await PaymentConfirmation_1.PaymentConfirmation.findByIdAndUpdate(confirmationId, updateData, { new: true }).populate('user', 'firstName lastName name')
            .populate('order', 'orderNumber total status')
            .populate('appointment', 'date time service status');
    }
    /**
     * Get payment confirmation statistics
     */
    static async getPaymentConfirmationStats() {
        const stats = await PaymentConfirmation_1.PaymentConfirmation.aggregate([
            {
                $group: {
                    _id: '$status',
                    count: { $sum: 1 },
                    totalAmount: { $sum: '$amount' }
                }
            }
        ]);
        const result = {
            pending: { count: 0, totalAmount: 0 },
            verified: { count: 0, totalAmount: 0 },
            rejected: { count: 0, totalAmount: 0 }
        };
        stats.forEach(stat => {
            if (result[stat._id]) {
                result[stat._id] = {
                    count: stat.count,
                    totalAmount: stat.totalAmount
                };
            }
        });
        return result;
    }
}
exports.PaymentConfirmationService = PaymentConfirmationService;
