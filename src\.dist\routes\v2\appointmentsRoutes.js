"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const models_1 = require("../../models");
const auth_1 = require("../../middleware/auth");
const validation_1 = require("../../middleware/validation");
const validation_2 = require("../../utils/validation");
const response_1 = require("../../utils/response");
const router = (0, express_1.Router)();
// GET /api/v2/appointments - Get all appointments (admin only)
router.get('/', auth_1.authenticate, (0, auth_1.authorize)('admin'), (0, validation_1.validate)(validation_2.paginationValidation), async (req, res) => {
    try {
        const { status, date, page = 1, limit = 20, search } = req.query;
        const filter = {};
        if (status) {
            filter.status = status;
        }
        if (date) {
            const searchDate = new Date(date);
            filter.date = {
                $gte: searchDate,
                $lt: new Date(searchDate.getTime() + 24 * 60 * 60 * 1000)
            };
        }
        if (search) {
            filter.$or = [
                { 'customerInfo.name': { $regex: search, $options: 'i' } },
                { 'customerInfo.email': { $regex: search, $options: 'i' } },
                { 'customerInfo.phone': { $regex: search, $options: 'i' } }
            ];
        }
        const pageNum = Number(page);
        const limitNum = Number(limit);
        const skip = (pageNum - 1) * limitNum;
        const [appointments, total] = await Promise.all([
            models_1.Appointment.find(filter)
                .populate('user', 'name email phone')
                .populate('service', 'name duration price category')
                .sort({ date: -1, time: -1 })
                .skip(skip)
                .limit(limitNum),
            models_1.Appointment.countDocuments(filter)
        ]);
        const totalPages = Math.ceil(total / limitNum);
        // Format appointments for v2 response
        const formattedAppointments = appointments.map(appointment => ({
            id: appointment._id,
            serviceId: appointment.service?._id,
            serviceName: appointment.service?.name,
            servicePrice: appointment.service?.price,
            serviceDuration: appointment.service?.duration,
            serviceCategory: appointment.service?.category,
            date: appointment.date,
            time: appointment.time,
            status: appointment.status,
            customerInfo: appointment.customerInfo,
            user: appointment.user ? {
                id: appointment.user._id,
                name: appointment.user.name,
                email: appointment.user.email,
                phone: appointment.user.phone
            } : null,
            notes: appointment.message,
            createdAt: appointment.createdAt,
            updatedAt: appointment.updatedAt
        }));
        (0, response_1.sendSuccess)(res, 'Appointments retrieved successfully', {
            appointments: formattedAppointments,
            pagination: {
                currentPage: pageNum,
                totalPages,
                totalItems: total,
                itemsPerPage: limitNum,
                hasNextPage: pageNum < totalPages,
                hasPrevPage: pageNum > 1
            }
        });
    }
    catch (error) {
        console.error('Get all appointments error:', error);
        (0, response_1.sendError)(res, error.message);
    }
});
// GET /api/v2/appointments/availability - Get available time slots
router.get('/availability', async (req, res) => {
    try {
        const { date, serviceId } = req.query;
        if (!date) {
            (0, response_1.sendError)(res, 'Date is required', undefined, 400);
            return;
        }
        const selectedDate = new Date(date);
        const startOfDay = new Date(selectedDate);
        startOfDay.setHours(0, 0, 0, 0);
        const endOfDay = new Date(selectedDate);
        endOfDay.setHours(23, 59, 59, 999);
        // Get existing appointments for the date
        const existingAppointments = await models_1.Appointment.find({
            date: {
                $gte: startOfDay,
                $lte: endOfDay
            },
            status: { $in: ['pending', 'confirmed'] }
        }).select('time');
        const bookedTimes = existingAppointments.map(apt => apt.time);
        // Generate available time slots (9 AM to 6 PM)
        const availableSlots = [];
        for (let hour = 9; hour <= 18; hour++) {
            for (let minute = 0; minute < 60; minute += 30) {
                const timeSlot = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
                if (!bookedTimes.includes(timeSlot)) {
                    availableSlots.push(timeSlot);
                }
            }
        }
        (0, response_1.sendSuccess)(res, 'Available time slots retrieved successfully', {
            date: date,
            availableSlots: availableSlots
        });
    }
    catch (error) {
        console.error('Get availability error:', error);
        (0, response_1.sendError)(res, error.message);
    }
});
// POST /api/v2/appointments - Create new appointment (public endpoint)
router.post('/', async (req, res) => {
    try {
        const { serviceId, date, time, customerInfo, addOns = [], userId } = req.body;
        // Validate required fields
        if (!serviceId || !date || !time || !customerInfo) {
            (0, response_1.sendError)(res, 'Service, date, time, and customer info are required', undefined, 400);
            return;
        }
        // Decode time if it's URL encoded
        const decodedTime = decodeURIComponent(time).replace(':undefined', '');
        // Find or create user based on email
        let user = null;
        if (userId) {
            user = await models_1.User.findById(userId);
        }
        else if (customerInfo.email) {
            // Check if user exists by email
            user = await models_1.User.findOne({ email: customerInfo.email.toLowerCase() });
            // If user doesn't exist, create one automatically
            if (!user) {
                try {
                    const bcrypt = require('bcrypt');
                    const tempPassword = Math.random().toString(36).slice(-8) + 'Temp!';
                    const hashedPassword = await bcrypt.hash(tempPassword, 10);
                    user = await models_1.User.create({
                        name: `${customerInfo.firstName} ${customerInfo.lastName}`,
                        email: customerInfo.email.toLowerCase(),
                        phone: customerInfo.phone || '',
                        role: 'customer',
                        isEmailVerified: false,
                        // Generate a temporary password - user can reset it later
                        password: hashedPassword
                    });
                    console.log(`Auto-created user for email: ${customerInfo.email}`);
                }
                catch (createError) {
                    console.error('Error creating user:', createError);
                    // Continue without user if creation fails
                }
            }
        }
        // Check if service exists
        const service = await models_1.Service.findById(serviceId);
        if (!service || !service.isActive) {
            (0, response_1.sendError)(res, 'Service not found or not available', undefined, 404);
            return;
        }
        // Parse the date and combine with time for a complete datetime
        const appointmentDate = new Date(date);
        // If time is provided, try to parse and combine with date
        let combinedDateTime = appointmentDate;
        if (decodedTime) {
            try {
                // Parse time (handle various formats)
                const timeMatch = decodedTime.match(/(\d{1,2}):(\d{2})\s*(AM|PM)?/i);
                if (timeMatch) {
                    let hours = parseInt(timeMatch[1]);
                    const minutes = parseInt(timeMatch[2]);
                    const period = timeMatch[3];
                    // Convert to 24-hour format if needed
                    if (period) {
                        if (period.toUpperCase() === 'PM' && hours !== 12) {
                            hours += 12;
                        }
                        else if (period.toUpperCase() === 'AM' && hours === 12) {
                            hours = 0;
                        }
                    }
                    // Set the time on the date
                    combinedDateTime = new Date(appointmentDate);
                    combinedDateTime.setHours(hours, minutes, 0, 0);
                }
            }
            catch (timeError) {
                console.error('Error parsing time:', timeError);
                // Use original date if time parsing fails
            }
        }
        // Check if time slot is available
        const existingAppointment = await models_1.Appointment.findOne({
            date: appointmentDate,
            time: decodedTime,
            status: { $in: ['pending', 'confirmed'] }
        });
        if (existingAppointment) {
            (0, response_1.sendError)(res, 'Time slot is already booked', undefined, 409);
            return;
        }
        // Calculate total price (for response only, not stored in appointment)
        let totalPrice = service.price;
        const addOnTotal = addOns.reduce((sum, addOn) => sum + (addOn.price || 0), 0);
        totalPrice += addOnTotal;
        // Create appointment
        const appointment = await models_1.Appointment.create({
            user: user ? user._id : null, // Use found/created user or null for guest bookings
            service: service._id,
            date: combinedDateTime, // Use combined date and time
            time: decodedTime,
            status: 'pending',
            type: 'service',
            customerInfo: {
                name: `${customerInfo.firstName} ${customerInfo.lastName}`,
                email: customerInfo.email,
                phone: customerInfo.phone
            },
            message: req.body.notes || ''
        });
        // Populate service data for response
        await appointment.populate('service', 'name price duration category');
        const responseData = {
            id: appointment._id,
            userId: user ? user._id : null,
            serviceId: appointment.service._id,
            serviceName: appointment.service.name,
            servicePrice: appointment.service.price,
            date: appointment.date,
            time: appointment.time,
            status: appointment.status,
            customerInfo: appointment.customerInfo,
            totalPrice: totalPrice,
            addOns: addOns,
            notes: appointment.message,
            createdAt: appointment.createdAt
        };
        (0, response_1.sendCreated)(res, 'Appointment created successfully', responseData);
    }
    catch (error) {
        console.error('Create appointment error:', error);
        (0, response_1.sendError)(res, error.message);
    }
});
// GET /api/v2/appointments/user - Get user dashboard data (public endpoint)
router.get('/user', async (req, res) => {
    try {
        const { email, userId } = req.query;
        if (!email && !userId) {
            (0, response_1.sendError)(res, 'Email or userId query parameter is required. Usage: ?email=<EMAIL> or ?userId=123', undefined, 400);
            return;
        }
        // Find user by email or userId
        let user = null;
        if (userId) {
            user = await models_1.User.findById(userId);
        }
        else if (email) {
            user = await models_1.User.findOne({ email: email.toString().toLowerCase() });
        }
        if (!user) {
            (0, response_1.sendError)(res, 'User not found', undefined, 404);
            return;
        }
        // Get all user appointments with full population
        const appointments = await models_1.Appointment.find({ user: user._id })
            .populate('service', 'name duration price category description')
            .populate('user', 'name email phone')
            .sort({ date: -1, time: -1 });
        // Get appointment statistics
        const appointmentStats = {
            total: appointments.length,
            pending: appointments.filter(apt => apt.status === 'pending').length,
            confirmed: appointments.filter(apt => apt.status === 'confirmed').length,
            completed: appointments.filter(apt => apt.status === 'completed').length,
            cancelled: appointments.filter(apt => apt.status === 'cancelled').length
        };
        // Get recent appointments (last 5)
        const recentAppointments = appointments.slice(0, 5).map(appointment => ({
            id: appointment._id,
            serviceId: appointment.service?._id,
            serviceName: appointment.service?.name,
            servicePrice: appointment.service?.price,
            serviceDuration: appointment.service?.duration,
            serviceCategory: appointment.service?.category,
            date: appointment.date,
            time: appointment.time,
            status: appointment.status,
            customerInfo: appointment.customerInfo,
            notes: appointment.message,
            createdAt: appointment.createdAt,
            updatedAt: appointment.updatedAt
        }));
        // Get upcoming appointments
        const now = new Date();
        const upcomingAppointments = appointments
            .filter(apt => {
            const appointmentDate = new Date(apt.date);
            return appointmentDate >= now && (apt.status === 'pending' || apt.status === 'confirmed');
        })
            .slice(0, 3)
            .map(appointment => ({
            id: appointment._id,
            serviceId: appointment.service?._id,
            serviceName: appointment.service?.name,
            servicePrice: appointment.service?.price,
            serviceDuration: appointment.service?.duration,
            date: appointment.date,
            time: appointment.time,
            status: appointment.status,
            notes: appointment.message
        }));
        // Calculate total spent
        const totalSpent = appointments
            .filter(apt => apt.status === 'completed')
            .reduce((sum, apt) => {
            const servicePrice = apt.service?.price || 0;
            return sum + servicePrice;
        }, 0);
        // Get favorite services (most booked)
        const serviceBookings = appointments.reduce((acc, apt) => {
            const serviceId = apt.service?._id?.toString();
            const serviceName = apt.service?.name;
            if (serviceId && serviceName) {
                if (!acc[serviceId]) {
                    acc[serviceId] = {
                        id: serviceId,
                        name: serviceName,
                        count: 0,
                        price: apt.service?.price || 0,
                        category: apt.service?.category || 'General'
                    };
                }
                acc[serviceId].count++;
            }
            return acc;
        }, {});
        const favoriteServices = Object.values(serviceBookings)
            .sort((a, b) => b.count - a.count)
            .slice(0, 3);
        // Remove password from user object
        const userResponse = user.toObject();
        const { password, ...userWithoutPassword } = userResponse;
        // Send comprehensive dashboard data
        (0, response_1.sendSuccess)(res, 'User dashboard data retrieved successfully', {
            user: userWithoutPassword,
            appointments: {
                all: appointments.map(appointment => ({
                    id: appointment._id,
                    serviceId: appointment.service?._id,
                    serviceName: appointment.service?.name,
                    servicePrice: appointment.service?.price,
                    serviceDuration: appointment.service?.duration,
                    serviceCategory: appointment.service?.category,
                    date: appointment.date,
                    time: appointment.time,
                    status: appointment.status,
                    customerInfo: appointment.customerInfo,
                    notes: appointment.message,
                    createdAt: appointment.createdAt,
                    updatedAt: appointment.updatedAt
                })),
                recent: recentAppointments,
                upcoming: upcomingAppointments
            },
            statistics: {
                appointments: appointmentStats,
                totalSpent,
                favoriteServices,
                memberSince: user.createdAt,
                lastActivity: user.updatedAt
            }
        });
    }
    catch (error) {
        console.error('Get user dashboard data error:', error);
        (0, response_1.sendError)(res, error.message);
    }
});
// GET /api/v2/appointments/my - Get user's appointments
router.get('/my', auth_1.authenticate, async (req, res) => {
    try {
        if (!req.user) {
            (0, response_1.sendError)(res, 'Authentication required', undefined, 401);
            return;
        }
        const appointments = await models_1.Appointment.find({ user: req.user._id })
            .populate('service', 'name price duration category')
            .sort({ date: -1, time: -1 });
        const formattedAppointments = appointments.map(appointment => ({
            id: appointment._id,
            serviceId: appointment.service._id,
            serviceName: appointment.service.name,
            servicePrice: appointment.service.price,
            date: appointment.date,
            time: appointment.time,
            status: appointment.status,
            customerInfo: appointment.customerInfo,
            totalPrice: appointment.service.price, // Use service price as total
            notes: appointment.message,
            createdAt: appointment.createdAt
        }));
        (0, response_1.sendSuccess)(res, 'User appointments retrieved successfully', formattedAppointments);
    }
    catch (error) {
        console.error('Get user appointments error:', error);
        (0, response_1.sendError)(res, error.message);
    }
});
// PUT /api/v2/appointments/:id - Update appointment
router.put('/:id', auth_1.authenticate, (0, validation_1.validate)((0, validation_2.mongoIdValidation)()), async (req, res) => {
    try {
        if (!req.user) {
            (0, response_1.sendError)(res, 'Authentication required', undefined, 401);
            return;
        }
        const { id } = req.params;
        const { date, time, customerInfo, notes } = req.body;
        const appointment = await models_1.Appointment.findOne({
            _id: id,
            user: req.user._id
        });
        if (!appointment) {
            (0, response_1.sendError)(res, 'Appointment not found', undefined, 404);
            return;
        }
        // Only allow updates to pending appointments
        if (appointment.status !== 'pending') {
            (0, response_1.sendError)(res, 'Cannot update confirmed or completed appointments', undefined, 400);
            return;
        }
        // Update fields
        if (date)
            appointment.date = new Date(date);
        if (time)
            appointment.time = time;
        if (customerInfo) {
            appointment.customerInfo = {
                name: customerInfo.firstName && customerInfo.lastName
                    ? `${customerInfo.firstName} ${customerInfo.lastName}`
                    : appointment.customerInfo.name,
                email: customerInfo.email || appointment.customerInfo.email,
                phone: customerInfo.phone || appointment.customerInfo.phone
            };
        }
        if (notes !== undefined)
            appointment.message = notes;
        await appointment.save();
        await appointment.populate('service', 'name price duration category');
        const responseData = {
            id: appointment._id,
            serviceId: appointment.service._id,
            serviceName: appointment.service.name,
            servicePrice: appointment.service.price,
            date: appointment.date,
            time: appointment.time,
            status: appointment.status,
            customerInfo: appointment.customerInfo,
            totalPrice: appointment.service.price,
            notes: appointment.message,
            updatedAt: appointment.updatedAt
        };
        (0, response_1.sendSuccess)(res, 'Appointment updated successfully', responseData);
    }
    catch (error) {
        console.error('Update appointment error:', error);
        (0, response_1.sendError)(res, error.message);
    }
});
// DELETE /api/v2/appointments/:id - Cancel appointment
router.delete('/:id', auth_1.authenticate, (0, validation_1.validate)((0, validation_2.mongoIdValidation)()), async (req, res) => {
    try {
        if (!req.user) {
            (0, response_1.sendError)(res, 'Authentication required', undefined, 401);
            return;
        }
        const { id } = req.params;
        const appointment = await models_1.Appointment.findOne({
            _id: id,
            user: req.user._id
        });
        if (!appointment) {
            (0, response_1.sendError)(res, 'Appointment not found', undefined, 404);
            return;
        }
        appointment.status = 'cancelled';
        await appointment.save();
        (0, response_1.sendSuccess)(res, 'Appointment cancelled successfully', { id: appointment._id });
    }
    catch (error) {
        console.error('Cancel appointment error:', error);
        (0, response_1.sendError)(res, error.message);
    }
});
exports.default = router;
