"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const authRoutes_1 = __importDefault(require("./authRoutes"));
const servicesRoutes_1 = __importDefault(require("./servicesRoutes"));
const appointmentsRoutes_1 = __importDefault(require("./appointmentsRoutes"));
const paymentConfirmationRoutes_1 = __importDefault(require("./paymentConfirmationRoutes"));
const userRoutes_1 = __importDefault(require("./userRoutes"));
const adminRoutes_1 = __importDefault(require("./adminRoutes"));
const router = (0, express_1.Router)();
// Mount v2 routes
router.use('/auth', authRoutes_1.default);
router.use('/services', servicesRoutes_1.default);
router.use('/appointments', appointmentsRoutes_1.default);
router.use('/payment-confirmations', paymentConfirmationRoutes_1.default);
router.use('/user', userRoutes_1.default);
router.use('/admin', adminRoutes_1.default);
// V2 Health check endpoint
router.get('/health', (_req, res) => {
    res.json({
        success: true,
        message: 'API v2 is running',
        version: '2.0.0',
        timestamp: new Date().toISOString(),
        environment: process.env.NODE_ENV || 'development',
        endpoints: {
            auth: '/api/v2/auth',
            services: '/api/v2/services',
            appointments: '/api/v2/appointments',
            paymentConfirmations: '/api/v2/payment-confirmations',
            user: '/api/v2/user',
            admin: '/api/v2/admin'
        }
    });
});
exports.default = router;
