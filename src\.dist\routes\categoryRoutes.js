"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const express_validator_1 = require("express-validator");
const categoryController_1 = require("../controllers/categoryController");
const auth_1 = require("../middleware/auth");
const router = express_1.default.Router();
// Validation rules
const categoryValidation = [
    (0, express_validator_1.body)('name')
        .trim()
        .isLength({ min: 1, max: 100 })
        .withMessage('Category name must be between 1 and 100 characters'),
    (0, express_validator_1.body)('type')
        .isIn(['product', 'service'])
        .withMessage('Type must be either product or service'),
    (0, express_validator_1.body)('description')
        .optional()
        .trim()
        .isLength({ max: 500 })
        .withMessage('Description cannot be more than 500 characters')
];
const updateCategoryValidation = [
    (0, express_validator_1.body)('name')
        .optional()
        .trim()
        .isLength({ min: 1, max: 100 })
        .withMessage('Category name must be between 1 and 100 characters'),
    (0, express_validator_1.body)('description')
        .optional()
        .trim()
        .isLength({ max: 500 })
        .withMessage('Description cannot be more than 500 characters'),
    (0, express_validator_1.body)('isActive')
        .optional()
        .isBoolean()
        .withMessage('isActive must be a boolean')
];
// Public routes (for getting categories)
router.get('/products', categoryController_1.getProductCategories);
router.get('/services', categoryController_1.getServiceCategories);
// Admin routes (require authentication and admin role)
router.use(auth_1.authenticate);
router.use((0, auth_1.authorize)('admin'));
router.get('/', categoryController_1.getCategories);
router.get('/:id', categoryController_1.getCategoryById);
router.post('/', categoryValidation, categoryController_1.createCategory);
router.put('/:id', updateCategoryValidation, categoryController_1.updateCategory);
router.delete('/:id', categoryController_1.deleteCategory);
exports.default = router;
