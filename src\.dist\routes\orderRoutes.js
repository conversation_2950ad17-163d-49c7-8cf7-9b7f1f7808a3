"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const controllers_1 = require("../controllers");
const auth_1 = require("../middleware/auth");
const validation_1 = require("../middleware/validation");
const validation_2 = require("../utils/validation");
const router = (0, express_1.Router)();
// POST /api/orders
router.post('/', auth_1.authenticate, (0, validation_1.validate)(validation_2.createOrderValidation), controllers_1.OrderController.createOrder);
// GET /api/orders
router.get('/', auth_1.authenticate, (0, validation_1.validate)(validation_2.paginationValidation), controllers_1.OrderController.getUserOrders);
// GET /api/orders/:id
router.get('/:id', auth_1.authenticate, (0, validation_1.validate)((0, validation_2.mongoIdValidation)()), controllers_1.OrderController.getOrderById);
// Admin routes
// PUT /api/orders/:id/status (admin only)
router.put('/:id/status', auth_1.authenticate, (0, auth_1.authorize)('admin'), (0, validation_1.validate)((0, validation_2.mongoIdValidation)()), controllers_1.OrderController.updateOrderStatus);
// GET /api/orders/admin/all (admin only)
router.get('/admin/all', auth_1.authenticate, (0, auth_1.authorize)('admin'), (0, validation_1.validate)(validation_2.paginationValidation), controllers_1.OrderController.getAllOrders);
exports.default = router;
