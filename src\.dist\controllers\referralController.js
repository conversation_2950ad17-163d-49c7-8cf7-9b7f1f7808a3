"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReferralController = void 0;
const models_1 = require("../models");
const response_1 = require("../utils/response");
class ReferralController {
    static async getUserReferral(req, res) {
        try {
            if (!req.user) {
                (0, response_1.sendError)(res, 'Authentication required', undefined, 401);
                return;
            }
            let referral = await models_1.Referral.findOne({ referrer: req.user._id })
                .populate('referredUsers.user', 'name email');
            if (!referral) {
                referral = await models_1.Referral.create({ referrer: req.user._id });
            }
            const settings = await models_1.ReferralSettings.findOne() || await models_1.ReferralSettings.create({});
            (0, response_1.sendSuccess)(res, 'User referral data retrieved successfully', {
                referralCode: referral.referralCode,
                totalReferrals: referral.totalReferrals,
                totalRewards: referral.totalRewards,
                referredUsers: referral.referredUsers,
                settings: {
                    referrerReward: settings.referrerReward,
                    referredReward: settings.referredReward,
                    minimumPurchase: settings.minimumPurchase
                }
            });
        }
        catch (error) {
            console.error('Get user referral error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async validateReferralCode(req, res) {
        try {
            const { code } = req.body;
            if (!code) {
                (0, response_1.sendError)(res, 'Referral code is required');
                return;
            }
            const referral = await models_1.Referral.findOne({
                referralCode: code.toUpperCase(),
                isActive: true
            }).populate('referrer', 'name');
            if (!referral) {
                (0, response_1.sendError)(res, 'Invalid referral code', undefined, 404);
                return;
            }
            const settings = await models_1.ReferralSettings.findOne() || await models_1.ReferralSettings.create({});
            (0, response_1.sendSuccess)(res, 'Referral code is valid', {
                referralCode: referral.referralCode,
                referrerName: referral.referrer.name,
                reward: settings.referredReward,
                minimumPurchase: settings.minimumPurchase
            });
        }
        catch (error) {
            console.error('Validate referral code error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async processReferral(referralCode, newUserId, orderAmount) {
        try {
            const referral = await models_1.Referral.findOne({
                referralCode: referralCode.toUpperCase(),
                isActive: true
            });
            if (!referral) {
                return false;
            }
            const settings = await models_1.ReferralSettings.findOne();
            if (!settings || !settings.isActive || orderAmount < settings.minimumPurchase) {
                return false;
            }
            // Check if user was already referred
            const existingReferral = referral.referredUsers.find(ref => ref.user.toString() === newUserId);
            if (existingReferral) {
                // Update existing referral to completed
                existingReferral.status = 'completed';
                existingReferral.rewardEarned = settings.referrerReward;
            }
            else {
                // Add new referral
                referral.referredUsers.push({
                    user: newUserId,
                    status: 'completed',
                    rewardEarned: settings.referrerReward
                });
            }
            referral.totalReferrals = referral.referredUsers.filter(ref => ref.status === 'completed').length;
            referral.totalRewards += settings.referrerReward;
            await referral.save();
            return true;
        }
        catch (error) {
            console.error('Process referral error:', error);
            return false;
        }
    }
    static async getReferralSettings(req, res) {
        try {
            let settings = await models_1.ReferralSettings.findOne();
            if (!settings) {
                settings = await models_1.ReferralSettings.create({});
            }
            (0, response_1.sendSuccess)(res, 'Referral settings retrieved successfully', settings);
        }
        catch (error) {
            console.error('Get referral settings error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async updateReferralSettings(req, res) {
        try {
            const updateData = req.body;
            let settings = await models_1.ReferralSettings.findOne();
            if (!settings) {
                settings = await models_1.ReferralSettings.create(updateData);
            }
            else {
                Object.assign(settings, updateData);
                await settings.save();
            }
            (0, response_1.sendSuccess)(res, 'Referral settings updated successfully', settings);
        }
        catch (error) {
            console.error('Update referral settings error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async getAllReferrals(req, res) {
        try {
            const { page = 1, limit = 20, active } = req.query;
            const filter = {};
            if (active !== undefined) {
                filter.isActive = active === 'true';
            }
            const pageNum = Number(page);
            const limitNum = Number(limit);
            const skip = (pageNum - 1) * limitNum;
            const [referrals, total] = await Promise.all([
                models_1.Referral.find(filter)
                    .populate('referrer', 'name email')
                    .populate('referredUsers.user', 'name email')
                    .sort({ totalReferrals: -1 })
                    .skip(skip)
                    .limit(limitNum),
                models_1.Referral.countDocuments(filter)
            ]);
            const totalPages = Math.ceil(total / limitNum);
            (0, response_1.sendSuccess)(res, 'Referrals retrieved successfully', {
                referrals,
                pagination: {
                    currentPage: pageNum,
                    totalPages,
                    totalItems: total,
                    itemsPerPage: limitNum,
                    hasNextPage: pageNum < totalPages,
                    hasPrevPage: pageNum > 1
                }
            });
        }
        catch (error) {
            console.error('Get all referrals error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
}
exports.ReferralController = ReferralController;
