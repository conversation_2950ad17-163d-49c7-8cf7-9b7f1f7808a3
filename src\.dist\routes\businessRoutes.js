"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const controllers_1 = require("../controllers");
const auth_1 = require("../middleware/auth");
const router = (0, express_1.Router)();
// GET /api/business-hours
router.get('/business-hours', controllers_1.BusinessController.getBusinessHours);
// Admin routes
// GET /api/admin/business-profile (admin only)
router.get('/business-profile', auth_1.authenticate, (0, auth_1.authorize)('admin'), controllers_1.BusinessController.getBusinessProfile);
// PUT /api/admin/business-profile (admin only)
router.put('/business-profile', auth_1.authenticate, (0, auth_1.authorize)('admin'), controllers_1.BusinessController.updateBusinessProfile);
// PUT /api/admin/business-hours (admin only)
router.put('/business-hours', auth_1.authenticate, (0, auth_1.authorize)('admin'), controllers_1.BusinessController.updateBusinessHours);
exports.default = router;
