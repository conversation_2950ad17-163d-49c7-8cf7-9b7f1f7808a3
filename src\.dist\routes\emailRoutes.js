"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const auth_1 = require("../middleware/auth");
const scheduledEmailService_1 = require("../services/scheduledEmailService");
const emailService_1 = require("../services/emailService");
const models_1 = require("../models");
const router = (0, express_1.Router)();
// All email routes require admin authentication
router.use(auth_1.authenticate);
router.use((0, auth_1.authorize)('admin'));
// Manual trigger for appointment reminders
router.post('/trigger/appointment-reminders', async (req, res) => {
    try {
        await scheduledEmailService_1.scheduledEmailService.triggerAppointmentReminders();
        res.json({
            success: true,
            message: 'Appointment reminders triggered successfully'
        });
    }
    catch (error) {
        console.error('Error triggering appointment reminders:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to trigger appointment reminders',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
// Manual trigger for weekly promotions
router.post('/trigger/weekly-promotions', async (req, res) => {
    try {
        await scheduledEmailService_1.scheduledEmailService.triggerWeeklyPromotions();
        res.json({
            success: true,
            message: 'Weekly promotions triggered successfully'
        });
    }
    catch (error) {
        console.error('Error triggering weekly promotions:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to trigger weekly promotions',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
// Manual trigger for birthday emails
router.post('/trigger/birthday-emails', async (req, res) => {
    try {
        await scheduledEmailService_1.scheduledEmailService.triggerBirthdayEmails();
        res.json({
            success: true,
            message: 'Birthday emails triggered successfully'
        });
    }
    catch (error) {
        console.error('Error triggering birthday emails:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to trigger birthday emails',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
// Manual trigger for customer appreciation emails
router.post('/trigger/customer-appreciation', async (req, res) => {
    try {
        await scheduledEmailService_1.scheduledEmailService.triggerCustomerAppreciation();
        res.json({
            success: true,
            message: 'Customer appreciation emails triggered successfully'
        });
    }
    catch (error) {
        console.error('Error triggering customer appreciation emails:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to trigger customer appreciation emails',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
// Send custom promotional email to specific users
router.post('/send/custom-promotion', async (req, res) => {
    try {
        const { userIds, promotion } = req.body;
        if (!userIds || !Array.isArray(userIds) || userIds.length === 0) {
            return res.status(400).json({
                success: false,
                message: 'userIds array is required'
            });
        }
        if (!promotion || !promotion.title || !promotion.description || !promotion.discount) {
            return res.status(400).json({
                success: false,
                message: 'Promotion object with title, description, and discount is required'
            });
        }
        // Get users
        const users = await models_1.User.find({ _id: { $in: userIds } });
        if (users.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'No users found with provided IDs'
            });
        }
        // Send emails to all users
        const results = [];
        for (const user of users) {
            try {
                await emailService_1.emailService.sendPromotionalEmail(user, promotion);
                results.push({ userId: user._id, email: user.email, status: 'sent' });
            }
            catch (error) {
                results.push({
                    userId: user._id,
                    email: user.email,
                    status: 'failed',
                    error: error instanceof Error ? error.message : 'Unknown error'
                });
            }
        }
        res.json({
            success: true,
            message: `Custom promotion sent to ${results.filter(r => r.status === 'sent').length} out of ${users.length} users`,
            results
        });
    }
    catch (error) {
        console.error('Error sending custom promotion:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to send custom promotion',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
// Send test email to admin
router.post('/test/welcome', async (req, res) => {
    try {
        const { email } = req.body;
        if (!email) {
            return res.status(400).json({
                success: false,
                message: 'Email address is required'
            });
        }
        // Create a test user object
        const testUser = {
            firstName: 'Test',
            lastName: 'User',
            name: 'Test User',
            email: email,
            phone: '555-0123',
            role: 'user'
        };
        await emailService_1.emailService.sendWelcomeEmail(testUser, 'TestPassword123');
        res.json({
            success: true,
            message: `Test welcome email sent to ${email}`
        });
    }
    catch (error) {
        console.error('Error sending test email:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to send test email',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
// Send test birthday email
router.post('/test/birthday', async (req, res) => {
    try {
        const { email } = req.body;
        if (!email) {
            return res.status(400).json({
                success: false,
                message: 'Email address is required'
            });
        }
        // Create a test user object
        const testUser = {
            firstName: 'Birthday',
            lastName: 'User',
            name: 'Birthday User',
            email: email,
            phone: '555-0123',
            role: 'user'
        };
        await emailService_1.emailService.sendBirthdayEmail(testUser);
        res.json({
            success: true,
            message: `Test birthday email sent to ${email}`
        });
    }
    catch (error) {
        console.error('Error sending test birthday email:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to send test birthday email',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
// Get email service status
router.get('/status', async (req, res) => {
    try {
        res.json({
            success: true,
            message: 'Email service is running',
            config: {
                smtpHost: process.env.SMTP_HOST || 'Not configured',
                smtpPort: process.env.SMTP_PORT || 'Not configured',
                smtpUser: process.env.SMTP_USER ? 'Configured' : 'Not configured',
                smtpPass: process.env.SMTP_PASS ? 'Configured' : 'Not configured'
            },
            scheduledJobs: {
                appointmentReminders: 'Daily at 9:00 AM',
                weeklyPromotions: 'Monday at 10:00 AM',
                birthdayEmails: '1st of every month at 9:00 AM',
                customerAppreciation: '1st of every quarter at 10:00 AM'
            }
        });
    }
    catch (error) {
        console.error('Error getting email service status:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to get email service status',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.default = router;
