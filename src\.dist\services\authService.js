"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const crypto_1 = __importDefault(require("crypto"));
const models_1 = require("../models");
const jwt_1 = require("../utils/jwt");
const emailService_1 = require("./emailService");
class AuthService {
    static async register(userData) {
        // Check if user already exists
        const existingUser = await models_1.User.findOne({
            $or: [
                { email: userData.email },
                ...(userData.phone ? [{ phone: userData.phone }] : [])
            ]
        });
        if (existingUser) {
            if (existingUser.email === userData.email) {
                throw new Error('User with this email already exists');
            }
            if (userData.phone && existingUser.phone === userData.phone) {
                throw new Error('User with this phone number already exists');
            }
        }
        // Create new user
        const user = await models_1.User.create(userData);
        // Generate tokens
        const payload = {
            userId: user._id,
            email: user.email,
            role: user.role
        };
        const token = (0, jwt_1.generateToken)(payload);
        const refreshToken = (0, jwt_1.generateRefreshToken)(payload);
        // Send welcome email asynchronously
        try {
            emailService_1.emailService.sendWelcomeEmail(user).catch(err => console.error('Failed to send welcome email:', err));
        }
        catch (error) {
            console.error('Email service error:', error);
            // Don't fail registration if email fails
        }
        return { user, token, refreshToken };
    }
    static async login(email, password) {
        // Find user with password field
        const user = await models_1.User.findOne({ email }).select('+password');
        if (!user || !(await user.comparePassword(password))) {
            throw new Error('Invalid email or password');
        }
        // Generate tokens
        const payload = {
            userId: user._id,
            email: user.email,
            role: user.role
        };
        const token = (0, jwt_1.generateToken)(payload);
        const refreshToken = (0, jwt_1.generateRefreshToken)(payload);
        // Remove password from user object
        user.password = undefined;
        return { user, token, refreshToken };
    }
    static async forgotPassword(email) {
        const user = await models_1.User.findOne({ email });
        if (!user) {
            throw new Error('No user found with this email address');
        }
        // Generate reset token
        const resetToken = crypto_1.default.randomBytes(32).toString('hex');
        const hashedToken = crypto_1.default.createHash('sha256').update(resetToken).digest('hex');
        // Save hashed token and expiry to user
        user.resetPasswordToken = hashedToken;
        user.resetPasswordExpires = new Date(Date.now() + 60 * 60 * 1000); // 1 hour
        await user.save();
        // TODO: Implement password reset email with new email service
        console.log(`Password reset token generated for ${user.email}: ${resetToken}`);
        return resetToken;
    }
    static async resetPassword(token, newPassword) {
        // Hash the token to compare with stored hash
        const hashedToken = crypto_1.default.createHash('sha256').update(token).digest('hex');
        const user = await models_1.User.findOne({
            resetPasswordToken: hashedToken,
            resetPasswordExpires: { $gt: new Date() }
        });
        if (!user) {
            throw new Error('Invalid or expired reset token');
        }
        // Update password and clear reset fields
        user.password = newPassword;
        user.resetPasswordToken = undefined;
        user.resetPasswordExpires = undefined;
        await user.save();
    }
    static async verifyToken(userId) {
        const user = await models_1.User.findById(userId);
        if (!user) {
            throw new Error('User not found');
        }
        return user;
    }
    static async blacklistToken(token) {
        try {
            // Decode token to get expiration time
            const decoded = (0, jwt_1.decodeToken)(token);
            if (!decoded || !decoded.exp) {
                throw new Error('Invalid token');
            }
            // Convert exp (seconds) to Date
            const expiresAt = new Date(decoded.exp * 1000);
            // Add token to blacklist
            await models_1.TokenBlacklist.create({
                token,
                expiresAt
            });
        }
        catch (error) {
            console.error('Error blacklisting token:', error);
            throw new Error('Failed to blacklist token');
        }
    }
    static async isTokenBlacklisted(token) {
        try {
            const blacklistedToken = await models_1.TokenBlacklist.findOne({ token });
            return !!blacklistedToken;
        }
        catch (error) {
            console.error('Error checking token blacklist:', error);
            return false;
        }
    }
}
exports.AuthService = AuthService;
