"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationService = void 0;
const email_1 = require("./email");
const sms_1 = require("./sms");
const models_1 = require("../models");
class NotificationService {
    static async sendNotification(data) {
        const results = {};
        let overallSuccess = true;
        // Send in-app notification first (always create for record keeping)
        if (data.channels.includes('in-app')) {
            try {
                const notification = await models_1.Notification.create({
                    user: data.userId,
                    title: data.title,
                    message: data.message,
                    type: data.type,
                    metadata: data.metadata
                });
                results.inApp = {
                    success: true,
                    notificationId: notification._id.toString()
                };
            }
            catch (error) {
                results.inApp = {
                    success: false,
                    error: error.message
                };
                overallSuccess = false;
            }
        }
        // Send email notification
        if (data.channels.includes('email')) {
            try {
                const emailResult = await this.sendEmailNotification(data);
                results.email = emailResult;
                if (!emailResult.success)
                    overallSuccess = false;
            }
            catch (error) {
                results.email = {
                    success: false,
                    error: error.message
                };
                overallSuccess = false;
            }
        }
        // Send SMS notification
        if (data.channels.includes('sms')) {
            try {
                const smsResult = await this.sendSMSNotification(data);
                results.sms = smsResult;
                if (!smsResult.success)
                    overallSuccess = false;
            }
            catch (error) {
                results.sms = {
                    success: false,
                    error: error.message
                };
                overallSuccess = false;
            }
        }
        // Send push notification
        if (data.channels.includes('push')) {
            try {
                const pushResult = await this.sendPushNotification(data);
                results.push = pushResult;
                if (!pushResult.success)
                    overallSuccess = false;
            }
            catch (error) {
                results.push = {
                    success: false,
                    error: error.message
                };
                overallSuccess = false;
            }
        }
        return { success: overallSuccess, results };
    }
    static async sendEmailNotification(data) {
        try {
            // Get user email
            const { User } = await Promise.resolve().then(() => __importStar(require('../models')));
            const user = await User.findById(data.userId).select('email name');
            if (!user || !user.email) {
                return { success: false, error: 'User email not found' };
            }
            // Try to get email template
            let emailContent = {
                subject: data.title,
                html: `<p>${data.message}</p>`,
                text: data.message
            };
            try {
                const template = await models_1.NotificationTemplate.findOne({
                    type: data.type,
                    channel: 'email',
                    isActive: true
                });
                if (template) {
                    emailContent.subject = this.replaceVariables(template.subject || data.title, data.templateVariables);
                    emailContent.html = this.replaceVariables(template.content, data.templateVariables);
                    emailContent.text = this.stripHtml(emailContent.html);
                }
            }
            catch (templateError) {
                console.warn('Failed to load email template, using default content:', templateError);
            }
            await (0, email_1.sendEmail)({
                to: user.email,
                subject: emailContent.subject,
                html: emailContent.html,
                text: emailContent.text
            });
            return { success: true };
        }
        catch (error) {
            return { success: false, error: error.message };
        }
    }
    static async sendSMSNotification(data) {
        try {
            // Get user phone
            const { User } = await Promise.resolve().then(() => __importStar(require('../models')));
            const user = await User.findById(data.userId).select('phone notificationPreferences');
            if (!user || !user.phone) {
                return { success: false, error: 'User phone not found' };
            }
            if (!user.notificationPreferences?.sms) {
                return { success: false, error: 'User has disabled SMS notifications' };
            }
            // Try to get SMS template
            let smsMessage = data.message;
            try {
                const template = await models_1.NotificationTemplate.findOne({
                    type: data.type,
                    channel: 'sms',
                    isActive: true
                });
                if (template) {
                    smsMessage = this.replaceVariables(template.content, data.templateVariables);
                }
            }
            catch (templateError) {
                console.warn('Failed to load SMS template, using default content:', templateError);
            }
            const result = await sms_1.SMSService.sendSMS({
                to: user.phone,
                message: smsMessage
            });
            return result;
        }
        catch (error) {
            return { success: false, error: error.message };
        }
    }
    static async sendPushNotification(data) {
        try {
            // Get user push tokens
            const { User } = await Promise.resolve().then(() => __importStar(require('../models')));
            const user = await User.findById(data.userId).select('pushTokens notificationPreferences');
            if (!user || !user.notificationPreferences?.push) {
                return { success: false, error: 'User has disabled push notifications' };
            }
            // In production, implement push notification service (Firebase, OneSignal, etc.)
            // Example Firebase implementation:
            // const admin = require('firebase-admin');
            // const message = {
            //   notification: {
            //     title: data.title,
            //     body: data.message
            //   },
            //   data: data.metadata || {},
            //   tokens: user.pushTokens || []
            // };
            // const response = await admin.messaging().sendMulticast(message);
            console.log(`Push notification would be sent to user ${data.userId}: ${data.title}`);
            return { success: true };
        }
        catch (error) {
            return { success: false, error: error.message };
        }
    }
    static replaceVariables(content, variables) {
        if (!variables)
            return content;
        let result = content;
        Object.keys(variables).forEach(key => {
            const placeholder = `{{${key}}}`;
            result = result.replace(new RegExp(placeholder, 'g'), variables[key]);
        });
        return result;
    }
    static stripHtml(html) {
        return html.replace(/<[^>]*>/g, '').replace(/\s+/g, ' ').trim();
    }
    // Convenience methods for common notification types
    static async sendAppointmentConfirmation(userId, appointmentDetails) {
        return this.sendNotification({
            userId,
            type: 'appointment',
            title: 'Appointment Confirmed',
            message: `Your appointment for ${appointmentDetails.serviceName} on ${appointmentDetails.date} at ${appointmentDetails.time} has been confirmed.`,
            channels: ['email', 'in-app'],
            templateVariables: appointmentDetails
        });
    }
    static async sendOrderUpdate(userId, orderDetails) {
        return this.sendNotification({
            userId,
            type: 'order',
            title: 'Order Update',
            message: `Your order #${orderDetails.orderNumber} is now ${orderDetails.status}.`,
            channels: ['email', 'sms', 'in-app'],
            templateVariables: orderDetails
        });
    }
    static async sendWelcomeMessage(userId, userDetails) {
        return this.sendNotification({
            userId,
            type: 'general',
            title: 'Welcome to MicroLocs!',
            message: `Welcome ${userDetails.name}! Thank you for joining MicroLocs. We're excited to help you with your hair care journey.`,
            channels: ['email', 'in-app'],
            templateVariables: userDetails
        });
    }
    static async sendPromotionalMessage(userId, promotionDetails) {
        return this.sendNotification({
            userId,
            type: 'promotion',
            title: promotionDetails.title,
            message: promotionDetails.description,
            channels: ['email', 'in-app'],
            templateVariables: promotionDetails
        });
    }
}
exports.NotificationService = NotificationService;
