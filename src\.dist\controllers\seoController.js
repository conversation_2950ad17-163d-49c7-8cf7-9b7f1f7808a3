"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SEOController = void 0;
const models_1 = require("../models");
const response_1 = require("../utils/response");
class SEOController {
    static async getSEOSettings(req, res) {
        try {
            let seoSettings = await models_1.SEO.findOne();
            if (!seoSettings) {
                // Create default SEO settings if none exist
                seoSettings = await models_1.SEO.create({
                    title: 'MicroLocs - Professional Hair Care Services',
                    description: 'Professional microloc and hair care services. Book appointments, shop premium products, and transform your hair with expert care.',
                    keywords: 'microlocs, hair care, professional hair services, dreadlocks, hair styling, hair products',
                    ogImage: '',
                    structuredData: {
                        "@context": "https://schema.org",
                        "@type": "LocalBusiness",
                        "name": "MicroLocs",
                        "description": "Professional hair care services",
                        "@id": "",
                        "url": "",
                        "telephone": "",
                        "address": {
                            "@type": "PostalAddress",
                            "streetAddress": "",
                            "addressLocality": "",
                            "addressRegion": "",
                            "postalCode": "",
                            "addressCountry": "US"
                        }
                    }
                });
            }
            (0, response_1.sendSuccess)(res, 'SEO settings retrieved successfully', seoSettings);
        }
        catch (error) {
            console.error('Get SEO settings error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async updateSEOSettings(req, res) {
        try {
            const updateData = req.body;
            let seoSettings = await models_1.SEO.findOne();
            if (!seoSettings) {
                seoSettings = await models_1.SEO.create(updateData);
            }
            else {
                Object.assign(seoSettings, updateData);
                await seoSettings.save();
            }
            (0, response_1.sendSuccess)(res, 'SEO settings updated successfully', seoSettings);
        }
        catch (error) {
            console.error('Update SEO settings error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
}
exports.SEOController = SEOController;
