import { Link, useNavigate } from 'react-router-dom';
import { type User } from '../../utils/api';

interface HeaderProps {
  currentUser: User | null;
  onLogout: () => void;
}

export default function Header({ currentUser, onLogout }: HeaderProps) {
  const navigate = useNavigate();

  const handleLogout = () => {
    onLogout();
    navigate('/');
  };

  return (
    <header className="header">
      <div className="header-content">
        <div className="header-left">
          <Link to="/" className="logo-link">
            <div className="logo-container">
              <div className="logo-placeholder">
                <span>D</span>
              </div>
              <h1 className="business-name-header">dammyspicybeauty</h1>
            </div>
          </Link>
        </div>
        
        <nav className="header-nav">
          {currentUser && (
            <div className="nav-links">
              <Link to="/appointments" className="nav-link">
                APPOINTMENTS
              </Link>
              {currentUser.role === 'admin' && (
                <>
                  <Link to="/customers" className="nav-link">
                    CUSTOMERS
                  </Link>
                  <Link to="/services" className="nav-link">
                    SERVICES
                  </Link>
                  <Link to="/settings" className="nav-link">
                    SETTINGS
                  </Link>
                </>
              )}
            </div>
          )}
        </nav>

        <div className="auth-links">
          {currentUser ? (
            <>
              <Link to="/dashboard" className="auth-link">
                DASHBOARD
              </Link>
              <button className="auth-link" onClick={handleLogout}>
                LOGOUT
              </button>
            </>
          ) : (
            <>
              <Link to="/signup" className="auth-link">
                SIGN UP
              </Link>
              <Link to="/login" className="auth-link">
                LOG IN
              </Link>
            </>
          )}
        </div>
      </div>
    </header>
  );
}
