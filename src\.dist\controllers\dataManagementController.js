"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DataManagementController = void 0;
const models_1 = require("../models");
const response_1 = require("../utils/response");
const cache_1 = require("../utils/cache");
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const json2csv_1 = require("json2csv");
class DataManagementController {
    // Export Data
    static async exportData(req, res) {
        try {
            const { type } = req.params;
            const { format = 'json' } = req.query;
            let data = [];
            let filename = '';
            switch (type) {
                case 'customers':
                    data = await models_1.User.find({ role: 'user' }).select('-password').lean();
                    filename = `customers_${Date.now()}`;
                    break;
                case 'orders':
                    data = await models_1.Order.find()
                        .populate('user', 'name email')
                        .populate('items.product', 'name')
                        .lean();
                    filename = `orders_${Date.now()}`;
                    break;
                case 'appointments':
                    data = await models_1.Appointment.find()
                        .populate('user', 'name email')
                        .populate('service', 'name')
                        .lean();
                    filename = `appointments_${Date.now()}`;
                    break;
                case 'products':
                    data = await models_1.Product.find().lean();
                    filename = `products_${Date.now()}`;
                    break;
                case 'services':
                    data = await models_1.Service.find().lean();
                    filename = `services_${Date.now()}`;
                    break;
                default:
                    (0, response_1.sendError)(res, 'Invalid export type');
                    return;
            }
            if (format === 'csv') {
                try {
                    const parser = new json2csv_1.Parser();
                    const csv = parser.parse(data);
                    res.setHeader('Content-Type', 'text/csv');
                    res.setHeader('Content-Disposition', `attachment; filename="${filename}.csv"`);
                    res.send(csv);
                }
                catch (error) {
                    (0, response_1.sendError)(res, 'Failed to generate CSV');
                }
            }
            else {
                res.setHeader('Content-Type', 'application/json');
                res.setHeader('Content-Disposition', `attachment; filename="${filename}.json"`);
                res.json(data);
            }
        }
        catch (error) {
            console.error('Export data error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    // Import Data
    static async importData(req, res) {
        try {
            const { type } = req.params;
            if (!req.file) {
                (0, response_1.sendError)(res, 'No file provided');
                return;
            }
            const fileContent = fs_1.default.readFileSync(req.file.path, 'utf8');
            let data;
            try {
                data = JSON.parse(fileContent);
            }
            catch (error) {
                (0, response_1.sendError)(res, 'Invalid JSON file');
                return;
            }
            if (!Array.isArray(data)) {
                (0, response_1.sendError)(res, 'Data must be an array');
                return;
            }
            let imported = 0;
            let errors = 0;
            switch (type) {
                case 'products':
                    for (const item of data) {
                        try {
                            await models_1.Product.create(item);
                            imported++;
                        }
                        catch (error) {
                            errors++;
                            console.error('Product import error:', error);
                        }
                    }
                    break;
                case 'services':
                    for (const item of data) {
                        try {
                            await models_1.Service.create(item);
                            imported++;
                        }
                        catch (error) {
                            errors++;
                            console.error('Service import error:', error);
                        }
                    }
                    break;
                default:
                    (0, response_1.sendError)(res, 'Invalid import type');
                    return;
            }
            // Clean up uploaded file
            fs_1.default.unlinkSync(req.file.path);
            (0, response_1.sendSuccess)(res, 'Data imported successfully', {
                imported,
                errors,
                total: data.length
            });
        }
        catch (error) {
            console.error('Import data error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    // Backup Database
    static async createBackup(req, res) {
        try {
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const backupData = {
                timestamp,
                users: await models_1.User.find().select('-password').lean(),
                products: await models_1.Product.find().lean(),
                services: await models_1.Service.find().lean(),
                orders: await models_1.Order.find().lean(),
                appointments: await models_1.Appointment.find().lean()
            };
            const backupDir = path_1.default.join(process.cwd(), 'backups');
            if (!fs_1.default.existsSync(backupDir)) {
                fs_1.default.mkdirSync(backupDir, { recursive: true });
            }
            const filename = `backup_${timestamp}.json`;
            const filepath = path_1.default.join(backupDir, filename);
            fs_1.default.writeFileSync(filepath, JSON.stringify(backupData, null, 2));
            (0, response_1.sendSuccess)(res, 'Backup created successfully', {
                filename,
                size: fs_1.default.statSync(filepath).size,
                url: `/backups/${filename}`
            });
        }
        catch (error) {
            console.error('Create backup error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    // Restore Database
    static async restoreBackup(req, res) {
        try {
            if (!req.file) {
                (0, response_1.sendError)(res, 'No backup file provided');
                return;
            }
            const fileContent = fs_1.default.readFileSync(req.file.path, 'utf8');
            let backupData;
            try {
                backupData = JSON.parse(fileContent);
            }
            catch (error) {
                (0, response_1.sendError)(res, 'Invalid backup file');
                return;
            }
            // Validate backup structure
            if (!backupData.timestamp || !backupData.users) {
                (0, response_1.sendError)(res, 'Invalid backup file structure');
                return;
            }
            // Production-ready backup restoration with proper safeguards
            const results = {
                users: 0,
                products: 0,
                services: 0,
                orders: 0,
                appointments: 0
            };
            // WARNING: This operation will replace existing data
            // In production, implement proper backup validation and user confirmation
            // Restore users (excluding existing admin users)
            if (backupData.users) {
                for (const userData of backupData.users) {
                    try {
                        // Skip if user already exists
                        const existingUser = await models_1.User.findOne({ email: userData.email });
                        if (!existingUser) {
                            await models_1.User.create(userData);
                            results.users++;
                        }
                    }
                    catch (error) {
                        console.error('User restore error:', error);
                    }
                }
            }
            // Restore products
            if (backupData.products) {
                for (const productData of backupData.products) {
                    try {
                        const existingProduct = await models_1.Product.findOne({ name: productData.name });
                        if (!existingProduct) {
                            await models_1.Product.create(productData);
                            results.products++;
                        }
                    }
                    catch (error) {
                        console.error('Product restore error:', error);
                    }
                }
            }
            // Restore services
            if (backupData.services) {
                for (const serviceData of backupData.services) {
                    try {
                        const existingService = await models_1.Service.findOne({ name: serviceData.name });
                        if (!existingService) {
                            await models_1.Service.create(serviceData);
                            results.services++;
                        }
                    }
                    catch (error) {
                        console.error('Service restore error:', error);
                    }
                }
            }
            // Restore orders (only if users exist)
            if (backupData.orders) {
                for (const orderData of backupData.orders) {
                    try {
                        const userExists = await models_1.User.findById(orderData.user);
                        if (userExists) {
                            await models_1.Order.create(orderData);
                            results.orders++;
                        }
                    }
                    catch (error) {
                        console.error('Order restore error:', error);
                    }
                }
            }
            // Restore appointments (only if users and services exist)
            if (backupData.appointments) {
                for (const appointmentData of backupData.appointments) {
                    try {
                        const userExists = await models_1.User.findById(appointmentData.user);
                        const serviceExists = await models_1.Service.findById(appointmentData.service);
                        if (userExists && serviceExists) {
                            await models_1.Appointment.create(appointmentData);
                            results.appointments++;
                        }
                    }
                    catch (error) {
                        console.error('Appointment restore error:', error);
                    }
                }
            }
            // Clean up uploaded file
            fs_1.default.unlinkSync(req.file.path);
            (0, response_1.sendSuccess)(res, 'Backup restored successfully', results);
        }
        catch (error) {
            console.error('Restore backup error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    // Cache Management
    static async clearCache(req, res) {
        try {
            const { type } = req.body;
            switch (type) {
                case 'all':
                    const allResult = cache_1.CacheService.clearAllCaches();
                    if (allResult.success) {
                        (0, response_1.sendSuccess)(res, 'All caches cleared successfully');
                    }
                    else {
                        (0, response_1.sendError)(res, allResult.error || 'Failed to clear all caches');
                    }
                    break;
                case 'images':
                    const imageResult = cache_1.CacheService.clearImageCache();
                    if (imageResult.success) {
                        (0, response_1.sendSuccess)(res, 'Image cache cleared successfully');
                    }
                    else {
                        (0, response_1.sendError)(res, imageResult.error || 'Failed to clear image cache');
                    }
                    break;
                case 'data':
                    const dataResult = cache_1.CacheService.clearDataCache();
                    if (dataResult.success) {
                        (0, response_1.sendSuccess)(res, 'Data cache cleared successfully');
                    }
                    else {
                        (0, response_1.sendError)(res, dataResult.error || 'Failed to clear data cache');
                    }
                    break;
                default:
                    (0, response_1.sendError)(res, 'Invalid cache type. Valid types: all, images, data');
                    return;
            }
        }
        catch (error) {
            console.error('Clear cache error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    // Cache Statistics
    static async getCacheStats(req, res) {
        try {
            const stats = cache_1.CacheService.getCacheStats();
            (0, response_1.sendSuccess)(res, 'Cache statistics retrieved successfully', stats);
        }
        catch (error) {
            console.error('Get cache stats error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    // Cleanup Expired Cache
    static async cleanupCache(req, res) {
        try {
            const result = cache_1.CacheService.cleanupExpiredCache();
            (0, response_1.sendSuccess)(res, 'Cache cleanup completed successfully', result);
        }
        catch (error) {
            console.error('Cache cleanup error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
}
exports.DataManagementController = DataManagementController;
