"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const controllers_1 = require("../controllers");
const auth_1 = require("../middleware/auth");
const validation_1 = require("../middleware/validation");
const validation_2 = require("../utils/validation");
const router = (0, express_1.Router)();
// GET /api/products
router.get('/', (0, validation_1.validate)(validation_2.paginationValidation), controllers_1.ProductController.getAllProducts);
// GET /api/products/categories
router.get('/categories', controllers_1.ProductController.getProductCategories);
// GET /api/products/:id
router.get('/:id', (0, validation_1.validate)((0, validation_2.mongoIdValidation)()), controllers_1.ProductController.getProductById);
// Admin routes
// POST /api/products (admin only)
router.post('/', auth_1.authenticate, (0, auth_1.authorize)('admin'), controllers_1.ProductController.createProduct);
// PUT /api/products/:id (admin only)
router.put('/:id', auth_1.authenticate, (0, auth_1.authorize)('admin'), (0, validation_1.validate)((0, validation_2.mongoIdValidation)()), controllers_1.ProductController.updateProduct);
// DELETE /api/products/:id (admin only)
router.delete('/:id', auth_1.authenticate, (0, auth_1.authorize)('admin'), (0, validation_1.validate)((0, validation_2.mongoIdValidation)()), controllers_1.ProductController.deleteProduct);
exports.default = router;
