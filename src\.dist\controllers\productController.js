"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProductController = void 0;
const models_1 = require("../models");
const response_1 = require("../utils/response");
class ProductController {
    static async getAllProducts(req, res) {
        try {
            const { category, page = 1, limit = 10, search, minPrice, maxPrice, sortBy = 'name', sortOrder = 'asc' } = req.query;
            const filter = { isActive: true };
            // Category filter
            if (category) {
                filter.category = category;
            }
            // Price range filter
            if (minPrice || maxPrice) {
                filter.price = {};
                if (minPrice)
                    filter.price.$gte = Number(minPrice);
                if (maxPrice)
                    filter.price.$lte = Number(maxPrice);
            }
            // Search filter
            if (search) {
                filter.$text = { $search: search };
            }
            // Pagination
            const pageNum = Number(page);
            const limitNum = Number(limit);
            const skip = (pageNum - 1) * limitNum;
            // Sort
            const sort = {};
            sort[sortBy] = sortOrder === 'desc' ? -1 : 1;
            const [products, total] = await Promise.all([
                models_1.Product.find(filter)
                    .sort(sort)
                    .skip(skip)
                    .limit(limitNum),
                models_1.Product.countDocuments(filter)
            ]);
            const totalPages = Math.ceil(total / limitNum);
            (0, response_1.sendSuccess)(res, 'Products retrieved successfully', {
                products,
                pagination: {
                    currentPage: pageNum,
                    totalPages,
                    totalItems: total,
                    itemsPerPage: limitNum,
                    hasNextPage: pageNum < totalPages,
                    hasPrevPage: pageNum > 1
                }
            });
        }
        catch (error) {
            console.error('Get all products error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async getProductById(req, res) {
        try {
            const { id } = req.params;
            const product = await models_1.Product.findById(id);
            if (!product) {
                (0, response_1.sendNotFound)(res, 'Product not found');
                return;
            }
            (0, response_1.sendSuccess)(res, 'Product retrieved successfully', product);
        }
        catch (error) {
            console.error('Get product by ID error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async getProductCategories(req, res) {
        try {
            const categories = await models_1.Product.distinct('category', { isActive: true });
            (0, response_1.sendSuccess)(res, 'Product categories retrieved successfully', categories);
        }
        catch (error) {
            console.error('Get product categories error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async createProduct(req, res) {
        try {
            const productData = req.body;
            const product = await models_1.Product.create(productData);
            (0, response_1.sendSuccess)(res, 'Product created successfully', product, 201);
        }
        catch (error) {
            console.error('Create product error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async updateProduct(req, res) {
        try {
            const { id } = req.params;
            const updateData = req.body;
            const product = await models_1.Product.findByIdAndUpdate(id, updateData, { new: true, runValidators: true });
            if (!product) {
                (0, response_1.sendNotFound)(res, 'Product not found');
                return;
            }
            (0, response_1.sendSuccess)(res, 'Product updated successfully', product);
        }
        catch (error) {
            console.error('Update product error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async deleteProduct(req, res) {
        try {
            const { id } = req.params;
            const product = await models_1.Product.findByIdAndDelete(id);
            if (!product) {
                (0, response_1.sendNotFound)(res, 'Product not found');
                return;
            }
            (0, response_1.sendSuccess)(res, 'Product deleted successfully');
        }
        catch (error) {
            console.error('Delete product error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
}
exports.ProductController = ProductController;
