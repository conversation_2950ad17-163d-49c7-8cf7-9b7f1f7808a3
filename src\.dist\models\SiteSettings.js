"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.SiteSettings = void 0;
const mongoose_1 = __importStar(require("mongoose"));
const siteSettingsSchema = new mongoose_1.Schema({
    general: {
        siteName: {
            type: String,
            default: 'MicroLocs',
            trim: true
        },
        siteDescription: {
            type: String,
            default: 'Professional hair care services',
            trim: true
        },
        contactEmail: {
            type: String,
            default: '<EMAIL>',
            trim: true,
            lowercase: true
        },
        contactPhone: {
            type: String,
            default: '',
            trim: true
        },
        address: {
            type: String,
            default: '',
            trim: true
        },
        timezone: {
            type: String,
            default: 'America/New_York',
            trim: true
        },
        currency: {
            type: String,
            default: 'USD',
            trim: true,
            uppercase: true
        },
        language: {
            type: String,
            default: 'en',
            trim: true,
            lowercase: true
        }
    },
    features: {
        enableAppointments: {
            type: Boolean,
            default: true
        },
        enableEcommerce: {
            type: Boolean,
            default: true
        },
        enableReviews: {
            type: Boolean,
            default: true
        },
        enableLoyaltyProgram: {
            type: Boolean,
            default: false
        },
        enableGiftCards: {
            type: Boolean,
            default: false
        },
        enableWaitlist: {
            type: Boolean,
            default: false
        },
        enableReferrals: {
            type: Boolean,
            default: false
        }
    },
    notifications: {
        emailNotifications: {
            type: Boolean,
            default: true
        },
        smsNotifications: {
            type: Boolean,
            default: false
        },
        pushNotifications: {
            type: Boolean,
            default: false
        },
        appointmentReminders: {
            type: Boolean,
            default: true
        },
        orderUpdates: {
            type: Boolean,
            default: true
        }
    },
    maintenance: {
        isMaintenanceMode: {
            type: Boolean,
            default: false
        },
        maintenanceMessage: {
            type: String,
            default: 'We are currently performing maintenance. Please check back soon.',
            trim: true
        },
        allowedIPs: [{
                type: String,
                trim: true
            }]
    },
    integrations: {
        googleAnalytics: {
            type: String,
            default: '',
            trim: true
        },
        facebookPixel: {
            type: String,
            default: '',
            trim: true
        },
        stripePublicKey: {
            type: String,
            default: '',
            trim: true
        },
        twilioAccountSid: {
            type: String,
            default: '',
            trim: true
        }
    }
}, {
    timestamps: true
});
exports.SiteSettings = mongoose_1.default.model('SiteSettings', siteSettingsSchema);
