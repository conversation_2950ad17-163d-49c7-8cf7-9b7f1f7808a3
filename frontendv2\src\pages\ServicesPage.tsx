import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import Header from '../components/Layout/Header'
import { retieServices, otherServices, addOnServices } from '../config/services'
import { type User } from '../utils/api'

interface ServicesPageProps {
  currentUser: User | null;
  onLogout: () => void;
}

export default function ServicesPage({ currentUser, onLogout }: ServicesPageProps) {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState<'main' | 'addons'>('main');

  useEffect(() => {
    if (!currentUser || currentUser.role !== 'admin') {
      navigate('/');
      return;
    }
  }, [currentUser, navigate]);

  const handleServiceEdit = (serviceId: string) => {
    // TODO: Implement service editing
    console.log('Edit service:', serviceId);
  };

  const handleServiceDelete = (serviceId: string) => {
    // TODO: Implement service deletion
    console.log('Delete service:', serviceId);
  };

  const handleAddService = () => {
    // TODO: Implement add new service
    console.log('Add new service');
  };

  const allMainServices = [...retieServices, ...otherServices];

  return (
    <div className="app">
      <Header currentUser={currentUser} onLogout={onLogout} />
      
      <main className="main-content">
        <div className="page-header">
          <h1>Services Management</h1>
          <button 
            className="btn-primary"
            onClick={handleAddService}
          >
            Add New Service
          </button>
        </div>

        <div className="service-tabs">
          <button
            className={`service-tab ${activeTab === 'main' ? 'active' : ''}`}
            onClick={() => setActiveTab('main')}
          >
            Main Services
          </button>
          <button
            className={`service-tab ${activeTab === 'addons' ? 'active' : ''}`}
            onClick={() => setActiveTab('addons')}
          >
            Add-on Services
          </button>
        </div>

        {activeTab === 'main' && (
          <div className="services-section">
            <h2>Main Services</h2>
            <div className="services-grid">
              {allMainServices.map(service => (
                <div key={service.id} className="service-card">
                  <div className="service-header">
                    <h3>{service.name}</h3>
                    <span className="service-price">${service.price}</span>
                  </div>
                  
                  <p className="service-description">{service.description}</p>
                  
                  {service.category && (
                    <span className="service-category">{service.category}</span>
                  )}
                  
                  <div className="service-actions">
                    <button 
                      className="btn-secondary"
                      onClick={() => handleServiceEdit(service.id)}
                    >
                      Edit
                    </button>
                    <button 
                      className="btn-danger"
                      onClick={() => handleServiceDelete(service.id)}
                    >
                      Delete
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'addons' && (
          <div className="services-section">
            <h2>Add-on Services</h2>
            <div className="addons-grid">
              {addOnServices.map(addon => (
                <div key={addon.id} className="addon-card">
                  <div className="addon-header">
                    <h3>{addon.name}</h3>
                    <span className="addon-price">${addon.price}</span>
                  </div>
                  
                  <div className="addon-details">
                    <p><strong>Duration:</strong> {addon.duration} minutes</p>
                    {addon.description && (
                      <p className="addon-description">{addon.description}</p>
                    )}
                  </div>
                  
                  <div className="addon-actions">
                    <button 
                      className="btn-secondary"
                      onClick={() => handleServiceEdit(addon.id)}
                    >
                      Edit
                    </button>
                    <button 
                      className="btn-danger"
                      onClick={() => handleServiceDelete(addon.id)}
                    >
                      Delete
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        <div className="services-stats">
          <div className="stat-card">
            <h3>Total Main Services</h3>
            <p className="stat-number">{allMainServices.length}</p>
          </div>
          <div className="stat-card">
            <h3>Total Add-ons</h3>
            <p className="stat-number">{addOnServices.length}</p>
          </div>
          <div className="stat-card">
            <h3>Price Range</h3>
            <p className="stat-number">
              ${Math.min(...allMainServices.map(s => parseFloat(s.price)))} - 
              ${Math.max(...allMainServices.map(s => parseFloat(s.price)))}
            </p>
          </div>
        </div>
      </main>
    </div>
  )
}
