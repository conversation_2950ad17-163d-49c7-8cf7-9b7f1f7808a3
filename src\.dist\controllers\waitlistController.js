"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.WaitlistController = void 0;
const models_1 = require("../models");
const response_1 = require("../utils/response");
const email_1 = require("../utils/email");
class WaitlistController {
    static async addToWaitlist(req, res) {
        try {
            const { name, email, phone, service, preferredDates } = req.body;
            // Verify service exists
            const serviceDoc = await models_1.Service.findById(service);
            if (!serviceDoc) {
                (0, response_1.sendNotFound)(res, 'Service not found');
                return;
            }
            // Check if user is already on waitlist for this service
            const existingEntry = await models_1.Waitlist.findOne({
                email,
                service,
                status: { $in: ['waiting', 'contacted'] }
            });
            if (existingEntry) {
                (0, response_1.sendError)(res, 'You are already on the waitlist for this service');
                return;
            }
            const waitlistEntry = await models_1.Waitlist.create({
                name,
                email,
                phone,
                service,
                preferredDates: preferredDates.map((date) => new Date(date))
            });
            await waitlistEntry.populate('service', 'name price duration');
            // Send confirmation email
            try {
                const emailContent = {
                    subject: 'Waitlist Confirmation - MicroLocs',
                    html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <h2 style="color: #333;">Waitlist Confirmation</h2>
              <p>Hello ${name},</p>
              <p>You have been successfully added to the waitlist for <strong>${serviceDoc.name}</strong>.</p>
              <p>We will contact you as soon as an appointment becomes available on one of your preferred dates.</p>
              <p>Thank you for your patience!</p>
              <p>Best regards,<br>The MicroLocs Team</p>
            </div>
          `,
                    text: `You have been added to the waitlist for ${serviceDoc.name}. We will contact you when an appointment becomes available.`
                };
                await (0, email_1.sendEmail)({
                    to: email,
                    subject: emailContent.subject,
                    html: emailContent.html,
                    text: emailContent.text
                });
            }
            catch (error) {
                console.error('Failed to send waitlist confirmation email:', error);
            }
            (0, response_1.sendCreated)(res, 'Successfully added to waitlist', waitlistEntry);
        }
        catch (error) {
            console.error('Add to waitlist error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async getWaitlist(req, res) {
        try {
            const { service, status, page = 1, limit = 20 } = req.query;
            const filter = {};
            if (service) {
                filter.service = service;
            }
            if (status) {
                filter.status = status;
            }
            const pageNum = Number(page);
            const limitNum = Number(limit);
            const skip = (pageNum - 1) * limitNum;
            const [waitlistEntries, total] = await Promise.all([
                models_1.Waitlist.find(filter)
                    .populate('service', 'name price duration')
                    .sort({ createdAt: 1 }) // First come, first served
                    .skip(skip)
                    .limit(limitNum),
                models_1.Waitlist.countDocuments(filter)
            ]);
            const totalPages = Math.ceil(total / limitNum);
            (0, response_1.sendSuccess)(res, 'Waitlist retrieved successfully', {
                waitlistEntries,
                pagination: {
                    currentPage: pageNum,
                    totalPages,
                    totalItems: total,
                    itemsPerPage: limitNum,
                    hasNextPage: pageNum < totalPages,
                    hasPrevPage: pageNum > 1
                }
            });
        }
        catch (error) {
            console.error('Get waitlist error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async updateWaitlistStatus(req, res) {
        try {
            const { id } = req.params;
            const { status, notes } = req.body;
            const waitlistEntry = await models_1.Waitlist.findByIdAndUpdate(id, { status, notes }, { new: true, runValidators: true }).populate('service', 'name');
            if (!waitlistEntry) {
                (0, response_1.sendNotFound)(res, 'Waitlist entry not found');
                return;
            }
            (0, response_1.sendSuccess)(res, 'Waitlist status updated successfully', waitlistEntry);
        }
        catch (error) {
            console.error('Update waitlist status error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async removeFromWaitlist(req, res) {
        try {
            const { id } = req.params;
            const waitlistEntry = await models_1.Waitlist.findByIdAndDelete(id);
            if (!waitlistEntry) {
                (0, response_1.sendNotFound)(res, 'Waitlist entry not found');
                return;
            }
            (0, response_1.sendSuccess)(res, 'Removed from waitlist successfully');
        }
        catch (error) {
            console.error('Remove from waitlist error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async notifyWaitlist(req, res) {
        try {
            const { service, date, message } = req.body;
            // Get waitlist entries for the service
            const waitlistEntries = await models_1.Waitlist.find({
                service,
                status: 'waiting'
            })
                .populate('service', 'name')
                .sort({ createdAt: 1 });
            let notificationsSent = 0;
            for (const entry of waitlistEntries) {
                try {
                    const serviceDoc = entry.service;
                    const emailContent = {
                        subject: 'Appointment Available - MicroLocs',
                        html: `
              <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <h2 style="color: #333;">Appointment Available!</h2>
                <p>Hello ${entry.name},</p>
                <p>Great news! An appointment slot has become available for <strong>${serviceDoc.name}</strong>.</p>
                <p><strong>Available Date:</strong> ${new Date(date).toDateString()}</p>
                ${message ? `<p><strong>Message:</strong> ${message}</p>` : ''}
                <p>Please contact us as soon as possible to book this appointment.</p>
                <p>Best regards,<br>The MicroLocs Team</p>
              </div>
            `,
                        text: `Appointment available for ${serviceDoc.name} on ${new Date(date).toDateString()}. Contact us to book!`
                    };
                    await (0, email_1.sendEmail)({
                        to: entry.email,
                        subject: emailContent.subject,
                        html: emailContent.html,
                        text: emailContent.text
                    });
                    // Update status to contacted
                    entry.status = 'contacted';
                    await entry.save();
                    notificationsSent++;
                }
                catch (error) {
                    console.error(`Failed to notify ${entry.email}:`, error);
                }
            }
            (0, response_1.sendSuccess)(res, 'Waitlist notifications sent successfully', {
                notificationsSent,
                totalEntries: waitlistEntries.length
            });
        }
        catch (error) {
            console.error('Notify waitlist error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
}
exports.WaitlistController = WaitlistController;
