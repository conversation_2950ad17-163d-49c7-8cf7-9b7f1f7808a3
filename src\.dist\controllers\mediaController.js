"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MediaController = void 0;
const mediaService_1 = require("../services/mediaService");
const response_1 = require("../utils/response");
class MediaController {
    /**
     * Upload media file
     */
    static async uploadMedia(req, res) {
        try {
            if (!req.file) {
                (0, response_1.sendError)(res, 'No file uploaded', undefined, 400);
                return;
            }
            const { alt, caption, description, tags } = req.body;
            const parsedTags = tags ? JSON.parse(tags) : [];
            const media = await mediaService_1.MediaService.uploadMedia(req.file, req.user._id, {
                alt,
                caption,
                description,
                tags: parsedTags
            });
            (0, response_1.sendSuccess)(res, 'Media uploaded successfully', { media });
        }
        catch (error) {
            console.error('Upload media error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    /**
     * Get media library
     */
    static async getMediaLibrary(req, res) {
        try {
            const { page = 1, limit = 20, search, mimeType, tags, uploadedBy } = req.query;
            const parsedTags = tags ? (typeof tags === 'string' ? [tags] : tags) : undefined;
            const result = await mediaService_1.MediaService.getMediaLibrary({
                page: Number(page),
                limit: Number(limit),
                search: search,
                mimeType: mimeType,
                tags: parsedTags,
                uploadedBy: uploadedBy
            });
            (0, response_1.sendSuccess)(res, 'Media library retrieved successfully', result);
        }
        catch (error) {
            console.error('Get media library error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    /**
     * Get media by ID
     */
    static async getMediaById(req, res) {
        try {
            const { id } = req.params;
            const media = await mediaService_1.MediaService.getMediaById(id);
            if (!media) {
                (0, response_1.sendError)(res, 'Media not found', undefined, 404);
                return;
            }
            (0, response_1.sendSuccess)(res, 'Media retrieved successfully', { media });
        }
        catch (error) {
            console.error('Get media by ID error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    /**
     * Update media metadata
     */
    static async updateMedia(req, res) {
        try {
            const { id } = req.params;
            const { alt, caption, description, tags } = req.body;
            const media = await mediaService_1.MediaService.updateMedia(id, {
                alt,
                caption,
                description,
                tags
            });
            if (!media) {
                (0, response_1.sendError)(res, 'Media not found', undefined, 404);
                return;
            }
            (0, response_1.sendSuccess)(res, 'Media updated successfully', { media });
        }
        catch (error) {
            console.error('Update media error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    /**
     * Delete media
     */
    static async deleteMedia(req, res) {
        try {
            const { id } = req.params;
            const success = await mediaService_1.MediaService.deleteMedia(id);
            if (!success) {
                (0, response_1.sendError)(res, 'Media not found or could not be deleted', undefined, 404);
                return;
            }
            (0, response_1.sendSuccess)(res, 'Media deleted successfully');
        }
        catch (error) {
            console.error('Delete media error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    /**
     * Bulk delete media
     */
    static async bulkDeleteMedia(req, res) {
        try {
            const { ids } = req.body;
            if (!Array.isArray(ids) || ids.length === 0) {
                (0, response_1.sendError)(res, 'Invalid or empty IDs array', undefined, 400);
                return;
            }
            const deletedCount = await mediaService_1.MediaService.bulkDeleteMedia(ids);
            (0, response_1.sendSuccess)(res, `${deletedCount} media files deleted successfully`, {
                deletedCount,
                totalRequested: ids.length
            });
        }
        catch (error) {
            console.error('Bulk delete media error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    /**
     * Get media statistics
     */
    static async getMediaStats(req, res) {
        try {
            const stats = await mediaService_1.MediaService.getMediaStats();
            (0, response_1.sendSuccess)(res, 'Media statistics retrieved successfully', { stats });
        }
        catch (error) {
            console.error('Get media stats error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    /**
     * Get all tags
     */
    static async getAllTags(req, res) {
        try {
            const tags = await mediaService_1.MediaService.getAllTags();
            (0, response_1.sendSuccess)(res, 'Tags retrieved successfully', { tags });
        }
        catch (error) {
            console.error('Get all tags error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    /**
     * Track media usage
     */
    static async trackMediaUsage(req, res) {
        try {
            const { mediaId } = req.params;
            const { type, id, field } = req.body;
            await mediaService_1.MediaService.trackMediaUsage(mediaId, { type, id, field });
            (0, response_1.sendSuccess)(res, 'Media usage tracked successfully');
        }
        catch (error) {
            console.error('Track media usage error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    /**
     * Remove media usage tracking
     */
    static async removeMediaUsage(req, res) {
        try {
            const { mediaId } = req.params;
            const { type, id, field } = req.body;
            await mediaService_1.MediaService.removeMediaUsage(mediaId, { type, id, field });
            (0, response_1.sendSuccess)(res, 'Media usage tracking removed successfully');
        }
        catch (error) {
            console.error('Remove media usage error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
}
exports.MediaController = MediaController;
