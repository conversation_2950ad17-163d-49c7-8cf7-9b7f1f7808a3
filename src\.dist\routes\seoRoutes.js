"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const controllers_1 = require("../controllers");
const auth_1 = require("../middleware/auth");
const router = (0, express_1.Router)();
// GET /api/admin/seo (admin only)
router.get('/', auth_1.authenticate, (0, auth_1.authorize)('admin'), controllers_1.SEOController.getSEOSettings);
// PUT /api/admin/seo (admin only)
router.put('/', auth_1.authenticate, (0, auth_1.authorize)('admin'), controllers_1.SEOController.updateSEOSettings);
exports.default = router;
