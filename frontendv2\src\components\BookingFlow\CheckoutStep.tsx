import { useNavigate, useSearchParams } from 'react-router-dom'
import Checkout from '../Checkout'
import { appointmentAPI } from '../../utils/appointmentAPI'

interface AddOnService {
  id: string;
  name: string;
  duration: number;
  price: number;
}

export default function CheckoutStep() {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  
  // Get booking data from URL params
  const serviceParam = searchParams.get('service');
  const addonsParam = searchParams.get('addons');
  const date = searchParams.get('date');
  const time = searchParams.get('time');
  const customerInfoParam = searchParams.get('customerInfo');
  
  let selectedService = null;
  let selectedAddOns: AddOnService[] = [];
  let customerInfo = null;
  
  if (serviceParam) {
    try {
      selectedService = JSON.parse(serviceParam);
    } catch (error) {
      console.error('Error parsing service:', error);
    }
  }
  
  if (addonsParam) {
    try {
      selectedAddOns = JSON.parse(addonsParam);
    } catch (error) {
      console.error('Error parsing addons:', error);
    }
  }
  
  if (customerInfoParam) {
    try {
      customerInfo = JSON.parse(customerInfoParam);
    } catch (error) {
      console.error('Error parsing customer info:', error);
    }
  }

  const booking = {
    selectedService,
    selectedAddOns,
    selectedDate: date || '',
    selectedTime: time ? decodeURIComponent(time) : '',
    step: 'checkout' as const,
    customerInfo
  };

  const handleBack = () => {
    const params = new URLSearchParams();
    params.set('service', JSON.stringify(selectedService));
    if (selectedAddOns.length > 0) {
      params.set('addons', JSON.stringify(selectedAddOns));
    }
    params.set('date', date || '');
    params.set('time', encodeURIComponent(time || ''));
    
    navigate(`/booking/details?${params.toString()}`);
  };

  const handleComplete = async () => {
    try {
      const totalPrice = parseFloat(selectedService?.price || '0') +
        selectedAddOns.reduce((sum, addon) => sum + addon.price, 0);

      const appointmentData = {
        serviceId: (selectedService?.id || selectedService?._id)?.toString() || '',
        serviceName: selectedService?.name || '',
        servicePrice: parseFloat(selectedService?.price || '0'),
        addOns: selectedAddOns,
        date: date || '',
        time: time || '',
        customerInfo: customerInfo,
        totalPrice
      };

      // Create appointment via API
      const response = await appointmentAPI.createAppointment(appointmentData);

      // Save user data to localStorage and navigate to success page
      if (response) {
        // Save user email and ID to localStorage for future use
        if (customerInfo?.email) {
          localStorage.setItem('userEmail', customerInfo.email);
        }
        if (response.userId) {
          localStorage.setItem('userId', response.userId);
        }

        navigate('/appointment-success', {
          state: {
            appointmentData: response
          }
        });
      } else {
        throw new Error('Failed to create appointment');
      }
    } catch (error) {
      console.error('Appointment booking error:', error);
      alert('Failed to book appointment. Please try again.');
    }
  };

  if (!selectedService || !date || !time || !customerInfo) {
    navigate('/');
    return null;
  }

  return (
    <Checkout 
      booking={booking}
      onBack={handleBack}
      onComplete={handleComplete}
    />
  );
}
