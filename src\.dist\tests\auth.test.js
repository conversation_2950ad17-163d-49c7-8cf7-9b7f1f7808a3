"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const supertest_1 = __importDefault(require("supertest"));
const server_1 = __importDefault(require("../server"));
const models_1 = require("../models");
describe('Authentication Endpoints', () => {
    beforeEach(async () => {
        // Clean up users before each test
        await models_1.User.deleteMany({});
    });
    describe('POST /api/auth/register', () => {
        it('should register a new user successfully', async () => {
            const userData = {
                name: 'Test User',
                email: '<EMAIL>',
                phone: '+1234567890',
                password: 'password123',
                confirmPassword: 'password123'
            };
            const response = await (0, supertest_1.default)(server_1.default)
                .post('/api/auth/register')
                .send(userData)
                .expect(201);
            expect(response.body.success).toBe(true);
            expect(response.body.data.user.email).toBe(userData.email);
            expect(response.body.data.token).toBeDefined();
        });
        it('should return error for invalid email', async () => {
            const userData = {
                name: 'Test User',
                email: 'invalid-email',
                phone: '+1234567890',
                password: 'password123',
                confirmPassword: 'password123'
            };
            const response = await (0, supertest_1.default)(server_1.default)
                .post('/api/auth/register')
                .send(userData)
                .expect(400);
            expect(response.body.success).toBe(false);
        });
        it('should return error for password mismatch', async () => {
            const userData = {
                name: 'Test User',
                email: '<EMAIL>',
                phone: '+1234567890',
                password: 'password123',
                confirmPassword: 'different-password'
            };
            const response = await (0, supertest_1.default)(server_1.default)
                .post('/api/auth/register')
                .send(userData)
                .expect(400);
            expect(response.body.success).toBe(false);
        });
    });
    describe('POST /api/auth/login', () => {
        beforeEach(async () => {
            // Create a test user
            await models_1.User.create({
                name: 'Test User',
                email: '<EMAIL>',
                phone: '+1234567890',
                password: 'password123'
            });
        });
        it('should login successfully with valid credentials', async () => {
            const loginData = {
                email: '<EMAIL>',
                password: 'password123'
            };
            const response = await (0, supertest_1.default)(server_1.default)
                .post('/api/auth/login')
                .send(loginData)
                .expect(200);
            expect(response.body.success).toBe(true);
            expect(response.body.data.user.email).toBe(loginData.email);
            expect(response.body.data.token).toBeDefined();
        });
        it('should return error for invalid credentials', async () => {
            const loginData = {
                email: '<EMAIL>',
                password: 'wrong-password'
            };
            const response = await (0, supertest_1.default)(server_1.default)
                .post('/api/auth/login')
                .send(loginData)
                .expect(401);
            expect(response.body.success).toBe(false);
        });
    });
});
