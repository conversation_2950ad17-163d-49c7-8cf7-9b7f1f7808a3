"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.seedDatabase = void 0;
const database_1 = require("../config/database");
const models_1 = require("../models");
const config_1 = require("../config");
const seedUsers = async () => {
    console.log('Seeding users...');
    // Create admin user
    const adminExists = await models_1.User.findOne({ email: config_1.config.ADMIN.EMAIL });
    if (!adminExists) {
        await models_1.User.create({
            name: 'Admin User',
            firstName: 'Admin',
            lastName: 'User',
            email: config_1.config.ADMIN.EMAIL,
            phone: '+1234567890',
            password: config_1.config.ADMIN.PASSWORD,
            role: 'admin',
            isVerified: true
        });
        console.log('Admin user created');
    }
    // Create sample users
    const sampleUsers = [
        {
            name: '<PERSON>',
            firstName: '<PERSON>',
            lastName: '<PERSON><PERSON>',
            email: '<EMAIL>',
            phone: '+1234567891',
            password: 'password123',
            role: 'user',
            isVerified: true
        },
        {
            name: 'Jane Smith',
            firstName: 'Jane',
            lastName: 'Smith',
            email: '<EMAIL>',
            phone: '+1234567892',
            password: 'password123',
            role: 'user',
            isVerified: true
        }
    ];
    for (const userData of sampleUsers) {
        const userExists = await models_1.User.findOne({ email: userData.email });
        if (!userExists) {
            await models_1.User.create(userData);
            console.log(`User ${userData.name} created`);
        }
    }
};
const seedServices = async () => {
    console.log('Seeding services...');
    const services = [
        {
            name: 'Hair Consultation',
            description: 'Professional hair analysis and styling consultation',
            category: 'consultation',
            duration: 60,
            price: 50,
            isActive: true
        },
        {
            name: 'Loc Maintenance',
            description: 'Professional dreadlock maintenance and care',
            category: 'treatment',
            duration: 120,
            price: 80,
            isActive: true
        },
        {
            name: 'Hair Treatment',
            description: 'Deep conditioning and repair treatment',
            category: 'treatment',
            duration: 90,
            price: 65,
            isActive: true
        },
        {
            name: 'Scalp Therapy',
            description: 'Therapeutic scalp massage and treatment',
            category: 'therapy',
            duration: 45,
            price: 40,
            isActive: true
        },
        {
            name: 'Hair Styling',
            description: 'Professional hair styling service',
            category: 'beauty',
            duration: 75,
            price: 55,
            isActive: true
        }
    ];
    for (const serviceData of services) {
        const serviceExists = await models_1.Service.findOne({ name: serviceData.name });
        if (!serviceExists) {
            await models_1.Service.create(serviceData);
            console.log(`Service ${serviceData.name} created`);
        }
    }
};
const seedProducts = async () => {
    console.log('Seeding products...');
    const products = [
        {
            name: 'Natural Hair Oil',
            description: 'Organic hair oil for nourishment and growth',
            category: 'haircare',
            price: 25.99,
            stock: 50,
            images: ['hair-oil.jpg'],
            isActive: true
        },
        {
            name: 'Loc Shampoo',
            description: 'Specialized shampoo for dreadlocks',
            category: 'haircare',
            price: 18.99,
            stock: 30,
            images: ['loc-shampoo.jpg'],
            isActive: true
        },
        {
            name: 'Scalp Moisturizer',
            description: 'Hydrating moisturizer for healthy scalp',
            category: 'skincare',
            price: 22.50,
            stock: 40,
            images: ['scalp-moisturizer.jpg'],
            isActive: true
        },
        {
            name: 'Hair Growth Serum',
            description: 'Advanced serum to promote hair growth',
            category: 'haircare',
            price: 35.00,
            stock: 25,
            images: ['growth-serum.jpg'],
            isActive: true
        },
        {
            name: 'Vitamin Supplements',
            description: 'Hair and nail vitamin supplements',
            category: 'supplements',
            price: 29.99,
            stock: 60,
            images: ['vitamins.jpg'],
            isActive: true
        },
        {
            name: 'Silk Hair Wrap',
            description: 'Protective silk wrap for hair care',
            category: 'accessories',
            price: 15.99,
            stock: 35,
            images: ['silk-wrap.jpg'],
            isActive: true
        }
    ];
    for (const productData of products) {
        const productExists = await models_1.Product.findOne({ name: productData.name });
        if (!productExists) {
            await models_1.Product.create(productData);
            console.log(`Product ${productData.name} created`);
        }
    }
};
const seedEmailTemplates = async () => {
    console.log('Seeding email templates...');
    const emailTemplates = [
        {
            type: 'appointment-confirmation',
            subject: '✅ Appointment Confirmed - \{{siteName}}',
            content: `
        <h2>Your Appointment is Confirmed!</h2>
        <p>Hi \{{customerName}},</p>
        <p>Your appointment has been successfully booked with us.</p>

        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3>Appointment Details:</h3>
          <p><strong>Service:</strong> \{{serviceName}}</p>
          <p><strong>Date:</strong> \{{appointmentDate}}</p>
          <p><strong>Time:</strong> \{{appointmentTime}}</p>
          <p><strong>Duration:</strong> \{{duration}}</p>
          <p><strong>Price:</strong> $\{{price}}</p>
        </div>

        <p>We look forward to seeing you!</p>
        <p>Best regards,<br>\{{siteName}} Team</p>
      `,
            variables: ['customerName', 'serviceName', 'appointmentDate', 'appointmentTime', 'duration', 'price', 'siteName']
        },
        {
            type: 'appointment-reminder',
            subject: '⏰ Appointment Reminder - Tomorrow at \{{appointmentTime}}',
            content: `
        <h2>Appointment Reminder</h2>
        <p>Hi \{{customerName}},</p>
        <p>This is a friendly reminder about your upcoming appointment.</p>

        <div style="background: #fff3cd; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3>Tomorrow's Appointment:</h3>
          <p><strong>Service:</strong> \{{serviceName}}</p>
          <p><strong>Date:</strong> \{{appointmentDate}}</p>
          <p><strong>Time:</strong> \{{appointmentTime}}</p>
          <p><strong>Location:</strong> \{{businessAddress}}</p>
        </div>

        <p>Please arrive 10 minutes early. If you need to reschedule, please contact us as soon as possible.</p>
        <p>See you soon!<br>\{{siteName}} Team</p>
      `,
            variables: ['customerName', 'serviceName', 'appointmentDate', 'appointmentTime', 'businessAddress', 'siteName']
        },
        {
            type: 'appointment-cancelled',
            subject: '❌ Appointment Cancelled - \{{siteName}}',
            content: `
        <h2>Appointment Cancelled</h2>
        <p>Hi \{{customerName}},</p>
        <p>Your appointment scheduled for \{{appointmentDate}} at \{{appointmentTime}} has been cancelled.</p>

        <div style="background: #f8d7da; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3>Cancelled Appointment:</h3>
          <p><strong>Service:</strong> \{{serviceName}}</p>
          <p><strong>Date:</strong> \{{appointmentDate}}</p>
          <p><strong>Time:</strong> \{{appointmentTime}}</p>
        </div>

        <p>\{{cancellationReason}}</p>
        <p>We apologize for any inconvenience. Please feel free to book a new appointment at your convenience.</p>
        <p>Best regards,<br>\{{siteName}} Team</p>
      `,
            variables: ['customerName', 'serviceName', 'appointmentDate', 'appointmentTime', 'cancellationReason', 'siteName']
        },
        {
            type: 'order-confirmation',
            subject: '🛍️ Order Confirmed #\{{orderNumber}} - \{{siteName}}',
            content: `
        <h2>Thank You for Your Order!</h2>
        <p>Hi \{{customerName}},</p>
        <p>Your order has been confirmed and is being processed.</p>

        <div style="background: #d4edda; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3>Order Details:</h3>
          <p><strong>Order Number:</strong> #\{{orderNumber}}</p>
          <p><strong>Order Date:</strong> \{{orderDate}}</p>
          <p><strong>Total Amount:</strong> $\{{totalAmount}}</p>
          <p><strong>Payment Method:</strong> \{{paymentMethod}}</p>
        </div>

        <h3>Items Ordered:</h3>
        \{{orderItems}}

        <p>You will receive a shipping confirmation email once your order is dispatched.</p>
        <p>Thank you for shopping with us!<br>\{{siteName}} Team</p>
      `,
            variables: ['customerName', 'orderNumber', 'orderDate', 'totalAmount', 'paymentMethod', 'orderItems', 'siteName']
        },
        {
            type: 'order-shipped',
            subject: '📦 Your Order #\{{orderNumber}} Has Shipped!',
            content: `
        <h2>Your Order is On Its Way!</h2>
        <p>Hi \{{customerName}},</p>
        <p>Great news! Your order has been shipped and is on its way to you.</p>

        <div style="background: #cce5ff; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3>Shipping Details:</h3>
          <p><strong>Order Number:</strong> #\{{orderNumber}}</p>
          <p><strong>Tracking Number:</strong> \{{trackingNumber}}</p>
          <p><strong>Carrier:</strong> \{{carrier}}</p>
          <p><strong>Estimated Delivery:</strong> \{{estimatedDelivery}}</p>
        </div>

        <p>You can track your package using the tracking number above.</p>
        <p>Thank you for your business!<br>\{{siteName}} Team</p>
      `,
            variables: ['customerName', 'orderNumber', 'trackingNumber', 'carrier', 'estimatedDelivery', 'siteName']
        },
        {
            type: 'order-delivered',
            subject: '✅ Your Order #\{{orderNumber}} Has Been Delivered!',
            content: `
        <h2>Order Delivered Successfully!</h2>
        <p>Hi \{{customerName}},</p>
        <p>Your order has been delivered! We hope you love your purchase.</p>

        <div style="background: #d4edda; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3>Delivery Confirmation:</h3>
          <p><strong>Order Number:</strong> #\{{orderNumber}}</p>
          <p><strong>Delivered On:</strong> \{{deliveryDate}}</p>
          <p><strong>Delivered To:</strong> \{{deliveryAddress}}</p>
        </div>

        <p>If you have any issues with your order, please don't hesitate to contact us.</p>
        <p>We'd love to hear about your experience - consider leaving us a review!</p>
        <p>Thank you for choosing \{{siteName}}!<br>\{{siteName}} Team</p>
      `,
            variables: ['customerName', 'orderNumber', 'deliveryDate', 'deliveryAddress', 'siteName']
        },
        {
            type: 'password-reset',
            subject: '🔐 Password Reset Request - \{{siteName}}',
            content: `
        <h2>Password Reset Request</h2>
        <p>Hi \{{customerName}},</p>
        <p>We received a request to reset your password for your \{{siteName}} account.</p>

        <div style="background: #fff3cd; padding: 20px; border-radius: 8px; margin: 20px 0; text-align: center;">
          <p>Click the button below to reset your password:</p>
          <a href="\{{resetLink}}" style="background: #f3d016; color: #000; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold;">Reset Password</a>
          <p style="margin-top: 15px; font-size: 12px;">This link will expire in 1 hour.</p>
        </div>

        <p>If you didn't request this password reset, please ignore this email.</p>
        <p>Best regards,<br>\{{siteName}} Team</p>
      `,
            variables: ['customerName', 'resetLink', 'siteName']
        },
        {
            type: 'welcome',
            subject: '🌟 Welcome to \{{siteName}} - Your Beauty Journey Begins!',
            content: `
        <h2>Welcome to \{{siteName}}!</h2>
        <p>Hi \{{customerName}},</p>
        <p>Welcome to our community! We're thrilled to have you join us on your beauty journey.</p>

        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3>What you can do with your account:</h3>
          <ul>
            <li>Book appointments with our professional stylists</li>
            <li>Browse and purchase our premium hair care products</li>
            <li>Track your appointment history and preferences</li>
            <li>Receive exclusive offers and updates</li>
          </ul>
        </div>

        \{{#if temporaryPassword}}
        <div style="background: #fff3cd; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <p><strong>Your temporary password:</strong> \{{temporaryPassword}}</p>
          <p>Please log in and change your password as soon as possible.</p>
        </div>
        \{{/if}}

        <p>If you have any questions, our team is here to help!</p>
        <p>Welcome aboard!<br>\{{siteName}} Team</p>
      `,
            variables: ['customerName', 'temporaryPassword', 'siteName']
        },
        {
            type: 'newsletter',
            subject: '📧 \{{newsletterTitle}} - \{{siteName}}',
            content: `
        <h2>\{{newsletterTitle}}</h2>
        <p>Hi \{{customerName}},</p>

        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
          \{{newsletterContent}}
        </div>

        <p>Thank you for being part of our community!</p>
        <p>Best regards,<br>\{{siteName}} Team</p>

        <div style="font-size: 12px; color: #666; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
          <p>You're receiving this email because you subscribed to our newsletter.</p>
          <p><a href="\{{unsubscribeLink}}">Unsubscribe</a> | <a href="\{{preferencesLink}}">Update Preferences</a></p>
        </div>
      `,
            variables: ['customerName', 'newsletterTitle', 'newsletterContent', 'unsubscribeLink', 'preferencesLink', 'siteName']
        },
        {
            type: 'promotion',
            subject: '🎉 \{{promotionTitle}} - Limited Time Offer!',
            content: `
        <h2>\{{promotionTitle}}</h2>
        <p>Hi \{{customerName}},</p>
        <p>Don't miss out on this amazing offer!</p>

        <div style="background: linear-gradient(135deg, #f3d016, #ffd700); padding: 30px; border-radius: 8px; margin: 20px 0; text-align: center; color: #000;">
          <h3 style="margin: 0 0 10px 0;">\{{promotionTitle}}</h3>
          <p style="font-size: 18px; margin: 0 0 15px 0;">\{{promotionDescription}}</p>
          <p style="font-size: 24px; font-weight: bold; margin: 0 0 15px 0;">\{{discountAmount}} OFF</p>
          <p style="margin: 0; font-size: 14px;">Use code: <strong>\{{promoCode}}</strong></p>
        </div>

        <div style="text-align: center; margin: 20px 0;">
          <a href="\{{shopLink}}" style="background: #000; color: #fff; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold;">Shop Now</a>
        </div>

        <p style="text-align: center; font-size: 14px; color: #666;">
          Offer valid until \{{expiryDate}}. Terms and conditions apply.
        </p>

        <p>Happy shopping!<br>\{{siteName}} Team</p>
      `,
            variables: ['customerName', 'promotionTitle', 'promotionDescription', 'discountAmount', 'promoCode', 'shopLink', 'expiryDate', 'siteName']
        }
    ];
    for (const templateData of emailTemplates) {
        const templateExists = await models_1.EmailTemplate.findOne({ type: templateData.type });
        if (!templateExists) {
            await models_1.EmailTemplate.create(templateData);
            console.log(`Email template ${templateData.type} created`);
        }
    }
};
const seedDatabase = async () => {
    try {
        console.log('Starting database seeding...');
        await (0, database_1.connectDatabase)();
        await seedUsers();
        await seedServices();
        await seedProducts();
        await seedEmailTemplates();
        console.log('Database seeding completed successfully!');
    }
    catch (error) {
        console.error('Error seeding database:', error);
    }
    finally {
        await (0, database_1.disconnectDatabase)();
    }
};
exports.seedDatabase = seedDatabase;
// Run seeding if this file is executed directly
if (require.main === module) {
    seedDatabase();
}
