"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.populateCategories = populateCategories;
const mongoose_1 = __importDefault(require("mongoose"));
const Category_1 = require("../models/Category");
const dotenv_1 = __importDefault(require("dotenv"));
// Load environment variables
dotenv_1.default.config();
const defaultProductCategories = [
    { name: 'Hair Care', description: 'Products for hair health and maintenance' },
    { name: 'Skin Care', description: 'Products for skin health and beauty' },
    { name: 'Locs Care', description: 'Specialized products for dreadlocks and locs' },
    { name: 'Natural Hair', description: 'Products for natural hair care and styling' },
    { name: 'Styling Products', description: 'Gels, creams, and styling aids' },
    { name: 'Tools', description: 'Hair tools and equipment' },
    { name: 'Accessories', description: 'Hair accessories and decorative items' },
    { name: 'Wellness', description: 'Health and wellness products' },
    { name: 'Supplements', description: 'Hair and health supplements' },
    { name: 'Equipment', description: 'Professional equipment and tools' },
    { name: 'Other', description: 'Miscellaneous products' }
];
const defaultServiceCategories = [
    { name: 'Hair Cut & Style', description: 'Cutting and styling services' },
    { name: 'Hair Color', description: 'Hair coloring and dyeing services' },
    { name: 'Hair Treatment', description: 'Deep conditioning and treatment services' },
    { name: 'Braiding', description: 'Traditional and modern braiding styles' },
    { name: 'Extensions', description: 'Hair extension installation and maintenance' },
    { name: 'Consultation', description: 'Hair consultation and analysis' },
    { name: 'Locs Installation', description: 'Starting new dreadlocks' },
    { name: 'Locs Maintenance', description: 'Maintaining existing dreadlocks' },
    { name: 'Locs Repair', description: 'Repairing damaged dreadlocks' },
    { name: 'Natural Hair Care', description: 'Services for natural hair' },
    { name: 'Scalp Treatment', description: 'Scalp health and treatment services' },
    { name: 'Deep Conditioning', description: 'Intensive hair conditioning treatments' },
    { name: 'Protective Styling', description: 'Protective hairstyles and techniques' },
    { name: 'Hair Washing', description: 'Professional hair washing services' },
    { name: 'Styling', description: 'General hair styling services' },
    { name: 'Color Touch-up', description: 'Hair color maintenance and touch-ups' },
    { name: 'Highlights', description: 'Hair highlighting services' },
    { name: 'Relaxer', description: 'Chemical hair relaxing services' },
    { name: 'Perm', description: 'Hair perming and texturizing' },
    { name: 'Keratin Treatment', description: 'Keratin smoothing treatments' },
    { name: 'Hair Analysis', description: 'Professional hair and scalp analysis' },
    { name: 'Other', description: 'Other specialized services' }
];
async function populateCategories() {
    try {
        // Connect to MongoDB
        const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/microlocs';
        await mongoose_1.default.connect(mongoUri);
        console.log('Connected to MongoDB');
        // Clear existing categories (optional - remove this if you want to keep existing ones)
        console.log('Clearing existing categories...');
        await Category_1.Category.deleteMany({});
        // Insert product categories
        console.log('Inserting product categories...');
        const productCategories = defaultProductCategories.map(cat => ({
            ...cat,
            type: 'product',
            isActive: true
        }));
        await Category_1.Category.insertMany(productCategories);
        console.log(`Inserted ${productCategories.length} product categories`);
        // Insert service categories
        console.log('Inserting service categories...');
        const serviceCategories = defaultServiceCategories.map(cat => ({
            ...cat,
            type: 'service',
            isActive: true
        }));
        await Category_1.Category.insertMany(serviceCategories);
        console.log(`Inserted ${serviceCategories.length} service categories`);
        console.log('Categories populated successfully!');
        // Display summary
        const totalProductCategories = await Category_1.Category.countDocuments({ type: 'product' });
        const totalServiceCategories = await Category_1.Category.countDocuments({ type: 'service' });
        console.log('\n=== SUMMARY ===');
        console.log(`Total Product Categories: ${totalProductCategories}`);
        console.log(`Total Service Categories: ${totalServiceCategories}`);
        console.log(`Total Categories: ${totalProductCategories + totalServiceCategories}`);
    }
    catch (error) {
        console.error('Error populating categories:', error);
    }
    finally {
        await mongoose_1.default.disconnect();
        console.log('Disconnected from MongoDB');
    }
}
// Run the script
if (require.main === module) {
    populateCategories();
}
