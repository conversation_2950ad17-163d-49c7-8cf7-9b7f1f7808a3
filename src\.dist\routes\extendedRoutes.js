"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const controllers_1 = require("../controllers");
const auth_1 = require("../middleware/auth");
const validation_1 = require("../middleware/validation");
const validation_2 = require("../utils/validation");
const multer_1 = __importDefault(require("multer"));
const router = (0, express_1.Router)();
const upload = (0, multer_1.default)({ dest: 'uploads/temp/' });
// Site Settings Routes
router.get('/settings', controllers_1.SiteSettingsController.getPublicSettings);
router.get('/admin/settings', auth_1.authenticate, (0, auth_1.authorize)('admin'), controllers_1.SiteSettingsController.getSiteSettings);
router.put('/admin/settings', auth_1.authenticate, (0, auth_1.authorize)('admin'), controllers_1.SiteSettingsController.updateSiteSettings);
// Staff Management Routes
router.get('/admin/staff', auth_1.authenticate, (0, auth_1.authorize)('admin'), (0, validation_1.validate)(validation_2.paginationValidation), controllers_1.StaffController.getAllStaff);
router.get('/admin/staff/:id', auth_1.authenticate, (0, auth_1.authorize)('admin'), (0, validation_1.validate)((0, validation_2.mongoIdValidation)()), controllers_1.StaffController.getStaffById);
router.post('/admin/staff', auth_1.authenticate, (0, auth_1.authorize)('admin'), controllers_1.StaffController.createStaff);
router.put('/admin/staff/:id', auth_1.authenticate, (0, auth_1.authorize)('admin'), (0, validation_1.validate)((0, validation_2.mongoIdValidation)()), controllers_1.StaffController.updateStaff);
router.delete('/admin/staff/:id', auth_1.authenticate, (0, auth_1.authorize)('admin'), (0, validation_1.validate)((0, validation_2.mongoIdValidation)()), controllers_1.StaffController.deleteStaff);
router.get('/admin/staff/:id/availability', auth_1.authenticate, (0, auth_1.authorize)('admin'), (0, validation_1.validate)((0, validation_2.mongoIdValidation)()), controllers_1.StaffController.getStaffAvailability);
router.put('/admin/staff/:id/availability', auth_1.authenticate, (0, auth_1.authorize)('admin'), (0, validation_1.validate)((0, validation_2.mongoIdValidation)()), controllers_1.StaffController.updateStaffAvailability);
// Service Add-ons Routes
router.get('/services/:id/add-ons', (0, validation_1.validate)((0, validation_2.mongoIdValidation)()), controllers_1.ServiceAddonController.getServiceAddons);
router.post('/admin/services/:id/add-ons', auth_1.authenticate, (0, auth_1.authorize)('admin'), (0, validation_1.validate)((0, validation_2.mongoIdValidation)()), controllers_1.ServiceAddonController.createServiceAddon);
router.put('/admin/services/add-ons/:addonId', auth_1.authenticate, (0, auth_1.authorize)('admin'), (0, validation_1.validate)((0, validation_2.mongoIdValidation)('addonId')), controllers_1.ServiceAddonController.updateServiceAddon);
router.delete('/admin/services/add-ons/:addonId', auth_1.authenticate, (0, auth_1.authorize)('admin'), (0, validation_1.validate)((0, validation_2.mongoIdValidation)('addonId')), controllers_1.ServiceAddonController.deleteServiceAddon);
// Waitlist Routes
router.post('/appointments/waitlist', controllers_1.WaitlistController.addToWaitlist);
router.get('/admin/waitlist', auth_1.authenticate, (0, auth_1.authorize)('admin'), (0, validation_1.validate)(validation_2.paginationValidation), controllers_1.WaitlistController.getWaitlist);
router.put('/admin/waitlist/:id', auth_1.authenticate, (0, auth_1.authorize)('admin'), (0, validation_1.validate)((0, validation_2.mongoIdValidation)()), controllers_1.WaitlistController.updateWaitlistStatus);
router.delete('/admin/waitlist/:id', auth_1.authenticate, (0, auth_1.authorize)('admin'), (0, validation_1.validate)((0, validation_2.mongoIdValidation)()), controllers_1.WaitlistController.removeFromWaitlist);
router.post('/admin/waitlist/notify', auth_1.authenticate, (0, auth_1.authorize)('admin'), controllers_1.WaitlistController.notifyWaitlist);
// Referral Program Routes
router.get('/users/referral', auth_1.authenticate, controllers_1.ReferralController.getUserReferral);
router.post('/referrals/validate', controllers_1.ReferralController.validateReferralCode);
router.get('/admin/referrals', auth_1.authenticate, (0, auth_1.authorize)('admin'), (0, validation_1.validate)(validation_2.paginationValidation), controllers_1.ReferralController.getAllReferrals);
router.get('/admin/referrals/settings', auth_1.authenticate, (0, auth_1.authorize)('admin'), controllers_1.ReferralController.getReferralSettings);
router.put('/admin/referrals/settings', auth_1.authenticate, (0, auth_1.authorize)('admin'), controllers_1.ReferralController.updateReferralSettings);
// Policy Routes
router.get('/policies/:type', controllers_1.PolicyController.getPolicy);
router.get('/admin/policies', auth_1.authenticate, (0, auth_1.authorize)('admin'), controllers_1.PolicyController.getAllPolicies);
router.put('/admin/policies/:type', auth_1.authenticate, (0, auth_1.authorize)('admin'), controllers_1.PolicyController.updatePolicy);
// Theme Settings Routes
router.get('/theme', controllers_1.ThemeController.getPublicTheme);
router.get('/admin/theme', auth_1.authenticate, (0, auth_1.authorize)('admin'), controllers_1.ThemeController.getThemeSettings);
router.put('/admin/theme', auth_1.authenticate, (0, auth_1.authorize)('admin'), controllers_1.ThemeController.updateThemeSettings);
router.post('/admin/theme/reset', auth_1.authenticate, (0, auth_1.authorize)('admin'), controllers_1.ThemeController.resetTheme);
// Notification Templates Routes
router.get('/admin/notification-templates', auth_1.authenticate, (0, auth_1.authorize)('admin'), controllers_1.NotificationTemplateController.getAllNotificationTemplates);
router.get('/admin/notification-templates/:type/:channel', auth_1.authenticate, (0, auth_1.authorize)('admin'), controllers_1.NotificationTemplateController.getNotificationTemplate);
router.put('/admin/notification-templates/:type/:channel', auth_1.authenticate, (0, auth_1.authorize)('admin'), controllers_1.NotificationTemplateController.updateNotificationTemplate);
router.delete('/admin/notification-templates/:type/:channel', auth_1.authenticate, (0, auth_1.authorize)('admin'), controllers_1.NotificationTemplateController.deleteNotificationTemplate);
// Data Management Routes
router.get('/admin/export/:type', auth_1.authenticate, (0, auth_1.authorize)('admin'), controllers_1.DataManagementController.exportData);
router.post('/admin/import/:type', auth_1.authenticate, (0, auth_1.authorize)('admin'), upload.single('file'), controllers_1.DataManagementController.importData);
router.post('/admin/backup', auth_1.authenticate, (0, auth_1.authorize)('admin'), controllers_1.DataManagementController.createBackup);
router.post('/admin/restore', auth_1.authenticate, (0, auth_1.authorize)('admin'), upload.single('backup'), controllers_1.DataManagementController.restoreBackup);
router.post('/admin/cache/clear', auth_1.authenticate, (0, auth_1.authorize)('admin'), controllers_1.DataManagementController.clearCache);
router.get('/admin/cache/stats', auth_1.authenticate, (0, auth_1.authorize)('admin'), controllers_1.DataManagementController.getCacheStats);
router.post('/admin/cache/cleanup', auth_1.authenticate, (0, auth_1.authorize)('admin'), controllers_1.DataManagementController.cleanupCache);
// Service Categories Routes
router.get('/services/categories', controllers_1.ServiceCategoryController.getServiceCategories);
router.post('/admin/services/categories', auth_1.authenticate, (0, auth_1.authorize)('admin'), controllers_1.ServiceCategoryController.createServiceCategory);
router.put('/admin/services/categories/:id', auth_1.authenticate, (0, auth_1.authorize)('admin'), controllers_1.ServiceCategoryController.updateServiceCategory);
router.delete('/admin/services/categories/:id', auth_1.authenticate, (0, auth_1.authorize)('admin'), controllers_1.ServiceCategoryController.deleteServiceCategory);
// Customer Notes Routes
router.get('/admin/customers/:id/notes', auth_1.authenticate, (0, auth_1.authorize)('admin'), (0, validation_1.validate)((0, validation_2.mongoIdValidation)()), controllers_1.CustomerNoteController.getCustomerNotes);
router.post('/admin/customers/:id/notes', auth_1.authenticate, (0, auth_1.authorize)('admin'), (0, validation_1.validate)((0, validation_2.mongoIdValidation)()), controllers_1.CustomerNoteController.createCustomerNote);
router.put('/admin/customers/notes/:noteId', auth_1.authenticate, (0, auth_1.authorize)('admin'), (0, validation_1.validate)((0, validation_2.mongoIdValidation)('noteId')), controllers_1.CustomerNoteController.updateCustomerNote);
router.delete('/admin/customers/notes/:noteId', auth_1.authenticate, (0, auth_1.authorize)('admin'), (0, validation_1.validate)((0, validation_2.mongoIdValidation)('noteId')), controllers_1.CustomerNoteController.deleteCustomerNote);
// Email Templates Routes
router.get('/admin/email-templates', auth_1.authenticate, (0, auth_1.authorize)('admin'), controllers_1.EmailTemplateController.getAllEmailTemplates);
router.get('/admin/email-templates/:type', auth_1.authenticate, (0, auth_1.authorize)('admin'), controllers_1.EmailTemplateController.getEmailTemplate);
router.put('/admin/email-templates/:type', auth_1.authenticate, (0, auth_1.authorize)('admin'), controllers_1.EmailTemplateController.updateEmailTemplate);
router.post('/admin/email-templates', auth_1.authenticate, (0, auth_1.authorize)('admin'), controllers_1.EmailTemplateController.createEmailTemplate);
router.delete('/admin/email-templates/:type', auth_1.authenticate, (0, auth_1.authorize)('admin'), controllers_1.EmailTemplateController.deleteEmailTemplate);
router.post('/admin/email-templates/:type/preview', auth_1.authenticate, (0, auth_1.authorize)('admin'), controllers_1.EmailTemplateController.previewEmailTemplate);
// Appointment Reminders Routes
router.post('/appointments/:id/remind', auth_1.authenticate, (0, auth_1.authorize)('admin'), (0, validation_1.validate)((0, validation_2.mongoIdValidation)()), controllers_1.AppointmentReminderController.sendReminder);
router.post('/admin/appointments/reminders/schedule', auth_1.authenticate, (0, auth_1.authorize)('admin'), controllers_1.AppointmentReminderController.scheduleAutomaticReminders);
exports.default = router;
