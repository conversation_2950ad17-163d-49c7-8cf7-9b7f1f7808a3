"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const controllers_1 = require("../controllers");
const auth_1 = require("../middleware/auth");
const validation_1 = require("../middleware/validation");
const validation_2 = require("../utils/validation");
const router = (0, express_1.Router)();
// POST /api/discount-codes/validate
router.post('/validate', controllers_1.DiscountController.validateDiscountCode);
// Admin routes
// GET /api/admin/discount-codes (admin only)
router.get('/', auth_1.authenticate, (0, auth_1.authorize)('admin'), (0, validation_1.validate)(validation_2.paginationValidation), controllers_1.DiscountController.getDiscountCodes);
// POST /api/admin/discount-codes (admin only)
router.post('/', auth_1.authenticate, (0, auth_1.authorize)('admin'), controllers_1.DiscountController.createDiscountCode);
// PUT /api/admin/discount-codes/:id (admin only)
router.put('/:id', auth_1.authenticate, (0, auth_1.authorize)('admin'), (0, validation_1.validate)((0, validation_2.mongoIdValidation)()), controllers_1.DiscountController.updateDiscountCode);
// DELETE /api/admin/discount-codes/:id (admin only)
router.delete('/:id', auth_1.authenticate, (0, auth_1.authorize)('admin'), (0, validation_1.validate)((0, validation_2.mongoIdValidation)()), controllers_1.DiscountController.deleteDiscountCode);
exports.default = router;
