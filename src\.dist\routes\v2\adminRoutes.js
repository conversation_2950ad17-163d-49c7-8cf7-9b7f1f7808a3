"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const models_1 = require("../../models");
const auth_1 = require("../../middleware/auth");
const response_1 = require("../../utils/response");
const router = (0, express_1.Router)();
// All admin routes require authentication and admin role
router.use(auth_1.authenticate);
router.use((0, auth_1.authorize)('admin'));
// GET /api/v2/admin/dashboard - Get admin dashboard overview
router.get('/dashboard', async (req, res) => {
    try {
        // Get counts for dashboard overview
        const totalUsers = await models_1.User.countDocuments({ role: 'user' });
        const totalAppointments = await models_1.Appointment.countDocuments();
        const totalServices = await models_1.Service.countDocuments({ isActive: true });
        const pendingPayments = await models_1.PaymentConfirmation.countDocuments({ status: 'pending' });
        // Get recent appointments
        const recentAppointments = await models_1.Appointment.find()
            .populate('user', 'firstName lastName email')
            .populate('service', 'name price')
            .sort({ createdAt: -1 })
            .limit(10);
        // Get pending payment confirmations
        const pendingPaymentConfirmations = await models_1.PaymentConfirmation.find({ status: 'pending' })
            .populate('user', 'firstName lastName email')
            .populate([
            { path: 'appointment', populate: { path: 'service', select: 'name' } },
            { path: 'order' }
        ])
            .sort({ createdAt: -1 })
            .limit(10);
        // Get appointment statistics by status
        const appointmentStats = await models_1.Appointment.aggregate([
            {
                $group: {
                    _id: '$status',
                    count: { $sum: 1 }
                }
            }
        ]);
        // Get payment confirmation statistics
        const paymentStats = await models_1.PaymentConfirmation.aggregate([
            {
                $group: {
                    _id: '$status',
                    count: { $sum: 1 },
                    totalAmount: { $sum: '$amount' }
                }
            }
        ]);
        // Format recent appointments
        const formattedAppointments = recentAppointments.map(appointment => ({
            id: appointment._id,
            user: {
                id: appointment.user._id,
                name: `${appointment.user.firstName} ${appointment.user.lastName}`,
                email: appointment.user.email
            },
            service: {
                id: appointment.service._id,
                name: appointment.service.name,
                price: appointment.service.price
            },
            date: appointment.date,
            time: appointment.time,
            status: appointment.status,
            totalAmount: appointment.service.price, // Use service price
            createdAt: appointment.createdAt
        }));
        // Format pending payment confirmations
        const formattedPaymentConfirmations = pendingPaymentConfirmations.map(confirmation => ({
            id: confirmation._id,
            user: {
                id: confirmation.user._id,
                name: `${confirmation.user.firstName} ${confirmation.user.lastName}`,
                email: confirmation.user.email
            },
            appointmentId: confirmation.appointment?._id,
            appointmentService: confirmation.appointment?.service?.name,
            orderId: confirmation.order?._id,
            amount: confirmation.amount,
            paymentMethod: confirmation.paymentMethod,
            proofImage: confirmation.proofImage,
            createdAt: confirmation.createdAt
        }));
        const dashboardData = {
            overview: {
                totalUsers,
                totalAppointments,
                totalServices,
                pendingPayments
            },
            statistics: {
                appointments: appointmentStats.reduce((acc, stat) => {
                    acc[stat._id] = stat.count;
                    return acc;
                }, {}),
                payments: paymentStats.reduce((acc, stat) => {
                    acc[stat._id] = {
                        count: stat.count,
                        totalAmount: stat.totalAmount
                    };
                    return acc;
                }, {})
            },
            recentAppointments: formattedAppointments,
            pendingPaymentConfirmations: formattedPaymentConfirmations
        };
        (0, response_1.sendSuccess)(res, 'Admin dashboard data retrieved successfully', dashboardData);
    }
    catch (error) {
        console.error('Get admin dashboard error:', error);
        (0, response_1.sendError)(res, error.message);
    }
});
// GET /api/v2/admin/appointments - Get all appointments with pagination and filters
router.get('/appointments', async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 20;
        const status = req.query.status;
        const search = req.query.search;
        const query = {};
        if (status) {
            query.status = status;
        }
        let appointments;
        let total;
        if (search) {
            // Search in user names and service names
            appointments = await models_1.Appointment.aggregate([
                {
                    $lookup: {
                        from: 'users',
                        localField: 'user',
                        foreignField: '_id',
                        as: 'user'
                    }
                },
                {
                    $lookup: {
                        from: 'services',
                        localField: 'service',
                        foreignField: '_id',
                        as: 'service'
                    }
                },
                {
                    $unwind: '$user'
                },
                {
                    $unwind: '$service'
                },
                {
                    $match: {
                        ...query,
                        $or: [
                            { 'user.firstName': { $regex: search, $options: 'i' } },
                            { 'user.lastName': { $regex: search, $options: 'i' } },
                            { 'user.email': { $regex: search, $options: 'i' } },
                            { 'service.name': { $regex: search, $options: 'i' } }
                        ]
                    }
                },
                { $sort: { createdAt: -1 } },
                { $skip: (page - 1) * limit },
                { $limit: limit }
            ]);
            total = await models_1.Appointment.aggregate([
                {
                    $lookup: {
                        from: 'users',
                        localField: 'user',
                        foreignField: '_id',
                        as: 'user'
                    }
                },
                {
                    $lookup: {
                        from: 'services',
                        localField: 'service',
                        foreignField: '_id',
                        as: 'service'
                    }
                },
                {
                    $unwind: '$user'
                },
                {
                    $unwind: '$service'
                },
                {
                    $match: {
                        ...query,
                        $or: [
                            { 'user.firstName': { $regex: search, $options: 'i' } },
                            { 'user.lastName': { $regex: search, $options: 'i' } },
                            { 'user.email': { $regex: search, $options: 'i' } },
                            { 'service.name': { $regex: search, $options: 'i' } }
                        ]
                    }
                },
                { $count: 'total' }
            ]);
            total = total[0]?.total || 0;
        }
        else {
            appointments = await models_1.Appointment.find(query)
                .populate('user', 'firstName lastName email phone')
                .populate('service', 'name price duration category')
                .sort({ createdAt: -1 })
                .skip((page - 1) * limit)
                .limit(limit);
            total = await models_1.Appointment.countDocuments(query);
        }
        const formattedAppointments = appointments.map((appointment) => ({
            id: appointment._id,
            user: {
                id: appointment.user._id,
                name: `${appointment.user.firstName} ${appointment.user.lastName}`,
                email: appointment.user.email,
                phone: appointment.user.phone
            },
            service: {
                id: appointment.service._id,
                name: appointment.service.name,
                price: appointment.service.price,
                duration: appointment.service.duration,
                category: appointment.service.category
            },
            date: appointment.date,
            time: appointment.time,
            status: appointment.status,
            customerInfo: appointment.customerInfo,
            totalAmount: appointment.service.price, // Use service price
            notes: appointment.message,
            createdAt: appointment.createdAt
        }));
        (0, response_1.sendSuccess)(res, 'Appointments retrieved successfully', {
            appointments: formattedAppointments,
            pagination: {
                page,
                limit,
                total,
                pages: Math.ceil(total / limit)
            }
        });
    }
    catch (error) {
        console.error('Get admin appointments error:', error);
        (0, response_1.sendError)(res, error.message);
    }
});
// PUT /api/v2/admin/appointments/:id/status - Update appointment status
router.put('/appointments/:id/status', async (req, res) => {
    try {
        const { id } = req.params;
        const { status } = req.body;
        if (!['pending', 'confirmed', 'completed', 'cancelled'].includes(status)) {
            (0, response_1.sendError)(res, 'Invalid status', undefined, 400);
            return;
        }
        const appointment = await models_1.Appointment.findById(id)
            .populate('user', 'firstName lastName email')
            .populate('service', 'name price');
        if (!appointment) {
            (0, response_1.sendError)(res, 'Appointment not found', undefined, 404);
            return;
        }
        appointment.status = status;
        await appointment.save();
        const responseData = {
            id: appointment._id,
            status: appointment.status,
            user: {
                name: `${appointment.user.firstName} ${appointment.user.lastName}`,
                email: appointment.user.email
            },
            service: {
                name: appointment.service.name
            },
            date: appointment.date,
            time: appointment.time,
            updatedAt: appointment.updatedAt
        };
        (0, response_1.sendSuccess)(res, 'Appointment status updated successfully', responseData);
    }
    catch (error) {
        console.error('Update appointment status error:', error);
        (0, response_1.sendError)(res, error.message);
    }
});
// GET /api/v2/admin/payment-confirmations - Get all payment confirmations with pagination and filters
router.get('/payment-confirmations', async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 20;
        const status = req.query.status;
        const query = {};
        if (status) {
            query.status = status;
        }
        const paymentConfirmations = await models_1.PaymentConfirmation.find(query)
            .populate('user', 'firstName lastName email phone')
            .populate([
            { path: 'appointment', populate: { path: 'service', select: 'name price' } },
            { path: 'order' }
        ])
            .sort({ createdAt: -1 })
            .skip((page - 1) * limit)
            .limit(limit);
        const total = await models_1.PaymentConfirmation.countDocuments(query);
        const formattedConfirmations = paymentConfirmations.map(confirmation => ({
            id: confirmation._id,
            user: {
                id: confirmation.user._id,
                name: `${confirmation.user.firstName} ${confirmation.user.lastName}`,
                email: confirmation.user.email,
                phone: confirmation.user.phone
            },
            appointmentId: confirmation.appointment?._id,
            appointmentService: confirmation.appointment?.service?.name,
            appointmentServicePrice: confirmation.appointment?.service?.price,
            orderId: confirmation.order?._id,
            amount: confirmation.amount,
            paymentMethod: confirmation.paymentMethod,
            proofImage: confirmation.proofImage,
            notes: confirmation.notes,
            status: confirmation.status,
            verifiedAt: confirmation.verifiedAt,
            rejectionReason: confirmation.rejectionReason,
            createdAt: confirmation.createdAt
        }));
        (0, response_1.sendSuccess)(res, 'Payment confirmations retrieved successfully', {
            paymentConfirmations: formattedConfirmations,
            pagination: {
                page,
                limit,
                total,
                pages: Math.ceil(total / limit)
            }
        });
    }
    catch (error) {
        console.error('Get admin payment confirmations error:', error);
        (0, response_1.sendError)(res, error.message);
    }
});
// PUT /api/v2/admin/payment-confirmations/:id/status - Update payment confirmation status
router.put('/payment-confirmations/:id/status', async (req, res) => {
    try {
        const { id } = req.params;
        const { status, rejectionReason } = req.body;
        if (!['verified', 'rejected'].includes(status)) {
            (0, response_1.sendError)(res, 'Invalid status. Must be "verified" or "rejected"', undefined, 400);
            return;
        }
        if (status === 'rejected' && !rejectionReason) {
            (0, response_1.sendError)(res, 'Rejection reason is required when rejecting payment confirmation', undefined, 400);
            return;
        }
        const paymentConfirmation = await models_1.PaymentConfirmation.findById(id)
            .populate('user', 'firstName lastName email')
            .populate([
            { path: 'appointment', populate: { path: 'service', select: 'name' } },
            { path: 'order' }
        ]);
        if (!paymentConfirmation) {
            (0, response_1.sendError)(res, 'Payment confirmation not found', undefined, 404);
            return;
        }
        paymentConfirmation.status = status;
        paymentConfirmation.verifiedBy = req.user._id;
        paymentConfirmation.verifiedAt = new Date();
        if (status === 'rejected') {
            paymentConfirmation.rejectionReason = rejectionReason;
        }
        await paymentConfirmation.save();
        const responseData = {
            id: paymentConfirmation._id,
            status: paymentConfirmation.status,
            user: {
                name: `${paymentConfirmation.user.firstName} ${paymentConfirmation.user.lastName}`,
                email: paymentConfirmation.user.email
            },
            amount: paymentConfirmation.amount,
            paymentMethod: paymentConfirmation.paymentMethod,
            verifiedAt: paymentConfirmation.verifiedAt,
            rejectionReason: paymentConfirmation.rejectionReason,
            updatedAt: paymentConfirmation.updatedAt
        };
        (0, response_1.sendSuccess)(res, 'Payment confirmation status updated successfully', responseData);
    }
    catch (error) {
        console.error('Update payment confirmation status error:', error);
        (0, response_1.sendError)(res, error.message);
    }
});
exports.default = router;
