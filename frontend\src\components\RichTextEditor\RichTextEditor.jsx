import { useState, useRef, useEffect } from 'react'
import { 
  FiBold, FiItalic, FiUnderline, FiList, FiLink, FiImage, 
  FiAlignLeft, FiAlignCenter, FiAlignRight, FiCode, FiType,
  FiEye, FiEdit3
} from 'react-icons/fi'

const RichTextEditor = ({
  value = '',
  onChange,
  placeholder = 'Enter content...',
  height = '400px',
  branding
}) => {
  const [content, setContent] = useState(value)
  const [isPreviewMode, setIsPreviewMode] = useState(false)
  const editorRef = useRef(null)

  useEffect(() => {
    setContent(value)
  }, [value])

  // Handle content change
  const handleContentChange = () => {
    if (editorRef.current) {
      const newContent = editorRef.current.innerHTML
      setContent(newContent)
      if (onChange) {
        onChange(newContent)
      }
    }
  }

  // Execute formatting command
  const execCommand = (command, value = null) => {
    document.execCommand(command, false, value)
    editorRef.current?.focus()
    handleContentChange()
  }

  // Insert link
  const insertLink = () => {
    const url = prompt('Enter URL:')
    if (url) {
      execCommand('createLink', url)
    }
  }

  // Insert image
  const insertImage = () => {
    const url = prompt('Enter image URL:')
    if (url) {
      execCommand('insertImage', url)
    }
  }

  // Format text size
  const formatTextSize = (size) => {
    execCommand('fontSize', size)
  }

  // Format text color
  const formatTextColor = (color) => {
    execCommand('foreColor', color)
  }

  // Format background color
  const formatBackgroundColor = (color) => {
    execCommand('backColor', color)
  }

  // Insert HTML
  const insertHTML = (html) => {
    execCommand('insertHTML', html)
  }

  // Common HTML snippets
  const insertHeading = (level) => {
    const text = window.getSelection().toString() || 'Heading'
    insertHTML(`<h${level}>${text}</h${level}>`)
  }

  const insertParagraph = () => {
    insertHTML('<p>New paragraph</p>')
  }

  const insertDivider = () => {
    insertHTML('<hr>')
  }

  const insertTable = () => {
    const tableHTML = `
      <table border="1" style="border-collapse: collapse; width: 100%;">
        <tr>
          <th style="padding: 8px; background-color: #f5f5f5;">Header 1</th>
          <th style="padding: 8px; background-color: #f5f5f5;">Header 2</th>
        </tr>
        <tr>
          <td style="padding: 8px;">Cell 1</td>
          <td style="padding: 8px;">Cell 2</td>
        </tr>
      </table>
    `
    insertHTML(tableHTML)
  }

  return (
    <div className="border border-gray-300 rounded-lg overflow-hidden">
      {/* Toolbar */}
      <div className="bg-gray-50 border-b border-gray-300 p-2">
        <div className="flex flex-wrap items-center gap-1">
          {/* Preview/Edit Toggle */}
          <div className="flex border border-gray-300 rounded overflow-hidden mr-2">
            <button
              type="button"
              onClick={() => setIsPreviewMode(false)}
              className={`px-3 py-1 text-sm ${!isPreviewMode ? 'bg-blue-500 text-white' : 'bg-white text-gray-700 hover:bg-gray-50'}`}
            >
              <FiEdit3 className="w-4 h-4 inline mr-1" />
              Edit
            </button>
            <button
              type="button"
              onClick={() => setIsPreviewMode(true)}
              className={`px-3 py-1 text-sm ${isPreviewMode ? 'bg-blue-500 text-white' : 'bg-white text-gray-700 hover:bg-gray-50'}`}
            >
              <FiEye className="w-4 h-4 inline mr-1" />
              Preview
            </button>
          </div>

          {!isPreviewMode && (
            <>
              {/* Text Formatting */}
              <div className="flex border border-gray-300 rounded overflow-hidden">
                <button
                  type="button"
                  onClick={() => execCommand('bold')}
                  className="p-2 hover:bg-gray-100 text-gray-700"
                  title="Bold"
                >
                  <FiBold className="w-4 h-4" />
                </button>
                <button
                  type="button"
                  onClick={() => execCommand('italic')}
                  className="p-2 hover:bg-gray-100 text-gray-700"
                  title="Italic"
                >
                  <FiItalic className="w-4 h-4" />
                </button>
                <button
                  type="button"
                  onClick={() => execCommand('underline')}
                  className="p-2 hover:bg-gray-100 text-gray-700"
                  title="Underline"
                >
                  <FiUnderline className="w-4 h-4" />
                </button>
              </div>

              {/* Headings */}
              <select
                onChange={(e) => {
                  if (e.target.value) {
                    if (e.target.value.startsWith('h')) {
                      insertHeading(e.target.value.charAt(1))
                    } else {
                      execCommand('formatBlock', e.target.value)
                    }
                    e.target.value = ''
                  }
                }}
                className="px-2 py-1 border border-gray-300 rounded text-sm"
                defaultValue=""
              >
                <option value="">Format</option>
                <option value="h1">Heading 1</option>
                <option value="h2">Heading 2</option>
                <option value="h3">Heading 3</option>
                <option value="h4">Heading 4</option>
                <option value="p">Paragraph</option>
                <option value="blockquote">Quote</option>
              </select>

              {/* Alignment */}
              <div className="flex border border-gray-300 rounded overflow-hidden">
                <button
                  type="button"
                  onClick={() => execCommand('justifyLeft')}
                  className="p-2 hover:bg-gray-100 text-gray-700"
                  title="Align Left"
                >
                  <FiAlignLeft className="w-4 h-4" />
                </button>
                <button
                  type="button"
                  onClick={() => execCommand('justifyCenter')}
                  className="p-2 hover:bg-gray-100 text-gray-700"
                  title="Align Center"
                >
                  <FiAlignCenter className="w-4 h-4" />
                </button>
                <button
                  type="button"
                  onClick={() => execCommand('justifyRight')}
                  className="p-2 hover:bg-gray-100 text-gray-700"
                  title="Align Right"
                >
                  <FiAlignRight className="w-4 h-4" />
                </button>
              </div>

              {/* Lists */}
              <div className="flex border border-gray-300 rounded overflow-hidden">
                <button
                  type="button"
                  onClick={() => execCommand('insertUnorderedList')}
                  className="p-2 hover:bg-gray-100 text-gray-700"
                  title="Bullet List"
                >
                  <FiList className="w-4 h-4" />
                </button>
                <button
                  type="button"
                  onClick={() => execCommand('insertOrderedList')}
                  className="p-2 hover:bg-gray-100 text-gray-700"
                  title="Numbered List"
                >
                  <FiType className="w-4 h-4" />
                </button>
              </div>

              {/* Links and Media */}
              <div className="flex border border-gray-300 rounded overflow-hidden">
                <button
                  type="button"
                  onClick={insertLink}
                  className="p-2 hover:bg-gray-100 text-gray-700"
                  title="Insert Link"
                >
                  <FiLink className="w-4 h-4" />
                </button>
                <button
                  type="button"
                  onClick={insertImage}
                  className="p-2 hover:bg-gray-100 text-gray-700"
                  title="Insert Image"
                >
                  <FiImage className="w-4 h-4" />
                </button>
              </div>

              {/* HTML Elements */}
              <select
                onChange={(e) => {
                  if (e.target.value) {
                    switch (e.target.value) {
                      case 'table':
                        insertTable()
                        break
                      case 'hr':
                        insertDivider()
                        break
                      case 'br':
                        insertHTML('<br>')
                        break
                      default:
                        break
                    }
                    e.target.value = ''
                  }
                }}
                className="px-2 py-1 border border-gray-300 rounded text-sm"
                defaultValue=""
              >
                <option value="">Insert</option>
                <option value="table">Table</option>
                <option value="hr">Horizontal Line</option>
                <option value="br">Line Break</option>
              </select>

              {/* Text Color */}
              <input
                type="color"
                onChange={(e) => formatTextColor(e.target.value)}
                className="w-8 h-8 border border-gray-300 rounded cursor-pointer"
                title="Text Color"
              />
            </>
          )}
        </div>
      </div>

      {/* Editor Content */}
      <div style={{ height }}>
        {isPreviewMode ? (
          <div 
            className="p-4 overflow-y-auto h-full prose max-w-none"
            dangerouslySetInnerHTML={{ __html: content }}
          />
        ) : (
          <div
            ref={editorRef}
            contentEditable
            className="p-4 outline-none h-full overflow-y-auto"
            style={{ minHeight: height }}
            onInput={handleContentChange}
            onBlur={handleContentChange}
            dangerouslySetInnerHTML={{ __html: content }}
            suppressContentEditableWarning={true}
          />
        )}
      </div>

      {/* Footer */}
      <div className="bg-gray-50 border-t border-gray-300 px-4 py-2">
        <div className="flex justify-between items-center text-xs text-gray-500">
          <span>
            {isPreviewMode ? 'Preview Mode' : 'Edit Mode'} • 
            Characters: {content.replace(/<[^>]*>/g, '').length}
          </span>
          <span>
            HTML content will be saved
          </span>
        </div>
      </div>
    </div>
  )
}

export default RichTextEditor
