"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserRoleController = void 0;
const models_1 = require("../models");
const response_1 = require("../utils/response");
class UserRoleController {
    static async getAllUsers(req, res) {
        try {
            const { page = 1, limit = 20, search, role } = req.query;
            const filter = {};
            if (search) {
                filter.$or = [
                    { name: { $regex: search, $options: 'i' } },
                    { email: { $regex: search, $options: 'i' } }
                ];
            }
            if (role) {
                filter.role = role;
            }
            else {
                // By default, exclude admin users from the response
                filter.role = 'user';
            }
            const pageNum = Number(page);
            const limitNum = Number(limit);
            const skip = (pageNum - 1) * limitNum;
            const [users, total] = await Promise.all([
                models_1.User.find(filter)
                    .select('-password')
                    .sort({ createdAt: -1 })
                    .skip(skip)
                    .limit(limitNum),
                models_1.User.countDocuments(filter)
            ]);
            const totalPages = Math.ceil(total / limitNum);
            (0, response_1.sendSuccess)(res, 'Users retrieved successfully', {
                users,
                pagination: {
                    currentPage: pageNum,
                    totalPages,
                    totalItems: total,
                    itemsPerPage: limitNum,
                    hasNextPage: pageNum < totalPages,
                    hasPrevPage: pageNum > 1
                }
            });
        }
        catch (error) {
            console.error('Get all users error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async updateUserRole(req, res) {
        try {
            const { id } = req.params;
            const { role } = req.body;
            if (!['admin', 'user'].includes(role)) {
                (0, response_1.sendError)(res, 'Invalid role. Must be either "admin" or "user"');
                return;
            }
            const user = await models_1.User.findByIdAndUpdate(id, { role }, { new: true, runValidators: true }).select('-password');
            if (!user) {
                (0, response_1.sendNotFound)(res, 'User not found');
                return;
            }
            (0, response_1.sendSuccess)(res, 'User role updated successfully', user);
        }
        catch (error) {
            console.error('Update user role error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async getUserById(req, res) {
        try {
            const { id } = req.params;
            const user = await models_1.User.findById(id).select('-password');
            if (!user) {
                (0, response_1.sendNotFound)(res, 'User not found');
                return;
            }
            (0, response_1.sendSuccess)(res, 'User retrieved successfully', user);
        }
        catch (error) {
            console.error('Get user by ID error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async deactivateUser(req, res) {
        try {
            const { id } = req.params;
            const user = await models_1.User.findByIdAndUpdate(id, { isActive: false }, { new: true }).select('-password');
            if (!user) {
                (0, response_1.sendNotFound)(res, 'User not found');
                return;
            }
            (0, response_1.sendSuccess)(res, 'User deactivated successfully', user);
        }
        catch (error) {
            console.error('Deactivate user error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async activateUser(req, res) {
        try {
            const { id } = req.params;
            const user = await models_1.User.findByIdAndUpdate(id, { isActive: true }, { new: true }).select('-password');
            if (!user) {
                (0, response_1.sendNotFound)(res, 'User not found');
                return;
            }
            (0, response_1.sendSuccess)(res, 'User activated successfully', user);
        }
        catch (error) {
            console.error('Activate user error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
}
exports.UserRoleController = UserRoleController;
