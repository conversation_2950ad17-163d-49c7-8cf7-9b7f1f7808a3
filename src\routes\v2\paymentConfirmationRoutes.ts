import { Router } from 'express';
import { Request, Response } from 'express';
import { PaymentConfirmation, Appointment, Order } from '../../models';
import { authenticate } from '../../middleware/auth';
import { UploadController } from '../../controllers';
import { sendSuccess, sendError, sendCreated } from '../../utils/response';
import { AuthenticatedRequest } from '../../types';

const router = Router();

// POST /api/v2/payment-confirmations - Create payment confirmation with image upload
router.post(
  '/',
  authenticate,
  UploadController.cloudinaryUploadSingle('proofImage'),
  async (req: AuthenticatedRequest, res: Response) => {
    try {
      if (!req.user) {
        sendError(res, 'Authentication required', undefined, 401);
        return;
      }

      const { appointmentId, orderId, amount, paymentMethod, notes } = req.body;

      // Validate required fields
      if (!amount || !paymentMethod) {
        sendError(res, 'Amount and payment method are required', undefined, 400);
        return;
      }

      if (!appointmentId && !orderId) {
        sendError(res, 'Either appointment ID or order ID is required', undefined, 400);
        return;
      }

      // Check if file was uploaded
      if (!(req as any).file) {
        sendError(res, 'Payment proof image is required', undefined, 400);
        return;
      }

      // Verify appointment or order exists
      if (appointmentId) {
        const appointment = await Appointment.findById(appointmentId);
        if (!appointment) {
          sendError(res, 'Appointment not found', undefined, 404);
          return;
        }

        // Check if user owns the appointment
        if (appointment.user.toString() !== req.user._id.toString()) {
          sendError(res, 'Unauthorized access to appointment', undefined, 403);
          return;
        }
      }

      if (orderId) {
        const order = await Order.findById(orderId);
        if (!order) {
          sendError(res, 'Order not found', undefined, 404);
          return;
        }

        // Check if user owns the order
        if (order.user.toString() !== req.user._id.toString()) {
          sendError(res, 'Unauthorized access to order', undefined, 403);
          return;
        }
      }

      // Create payment confirmation
      const paymentConfirmation = await PaymentConfirmation.create({
        user: req.user._id,
        appointment: appointmentId || undefined,
        order: orderId || undefined,
        amount: parseFloat(amount),
        paymentMethod: paymentMethod,
        proofImage: (req as any).file.path, // Cloudinary URL
        notes: notes || '',
        status: 'pending'
      });

      // Populate related data for response
      await paymentConfirmation.populate([
        { path: 'appointment', populate: { path: 'service', select: 'name' } },
        { path: 'order' }
      ]);

      const responseData = {
        id: paymentConfirmation._id,
        appointmentId: paymentConfirmation.appointment?._id,
        orderId: paymentConfirmation.order?._id,
        amount: paymentConfirmation.amount,
        paymentMethod: paymentConfirmation.paymentMethod,
        proofImage: paymentConfirmation.proofImage,
        notes: paymentConfirmation.notes,
        status: paymentConfirmation.status,
        createdAt: paymentConfirmation.createdAt
      };

      sendCreated(res, 'Payment confirmation submitted successfully', responseData);
    } catch (error) {
      console.error('Create payment confirmation error:', error);
      sendError(res, (error as Error).message);
    }
  }
);

// GET /api/v2/payment-confirmations/my - Get user's payment confirmations
router.get('/my', authenticate, async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (!req.user) {
      sendError(res, 'Authentication required', undefined, 401);
      return;
    }

    const paymentConfirmations = await PaymentConfirmation.find({ user: req.user._id })
      .populate([
        { path: 'appointment', populate: { path: 'service', select: 'name' } },
        { path: 'order' }
      ])
      .sort({ createdAt: -1 });

    const formattedConfirmations = paymentConfirmations.map(confirmation => ({
      id: confirmation._id,
      appointmentId: confirmation.appointment?._id,
      appointmentService: (confirmation.appointment as any)?.service?.name,
      orderId: confirmation.order?._id,
      amount: confirmation.amount,
      paymentMethod: confirmation.paymentMethod,
      proofImage: confirmation.proofImage,
      notes: confirmation.notes,
      status: confirmation.status,
      verifiedAt: confirmation.verifiedAt,
      rejectionReason: confirmation.rejectionReason,
      createdAt: confirmation.createdAt
    }));

    sendSuccess(res, 'Payment confirmations retrieved successfully', formattedConfirmations);
  } catch (error) {
    console.error('Get user payment confirmations error:', error);
    sendError(res, (error as Error).message);
  }
});

// GET /api/v2/payment-confirmations/:id - Get payment confirmation by ID
router.get('/:id', authenticate, async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (!req.user) {
      sendError(res, 'Authentication required', undefined, 401);
      return;
    }

    const { id } = req.params;

    const paymentConfirmation = await PaymentConfirmation.findOne({
      _id: id,
      user: req.user._id
    }).populate([
      { path: 'appointment', populate: { path: 'service', select: 'name price' } },
      { path: 'order' }
    ]);

    if (!paymentConfirmation) {
      sendError(res, 'Payment confirmation not found', undefined, 404);
      return;
    }

    const responseData = {
      id: paymentConfirmation._id,
      appointmentId: paymentConfirmation.appointment?._id,
      appointmentService: (paymentConfirmation.appointment as any)?.service?.name,
      appointmentServicePrice: (paymentConfirmation.appointment as any)?.service?.price,
      orderId: paymentConfirmation.order?._id,
      amount: paymentConfirmation.amount,
      paymentMethod: paymentConfirmation.paymentMethod,
      proofImage: paymentConfirmation.proofImage,
      notes: paymentConfirmation.notes,
      status: paymentConfirmation.status,
      verifiedAt: paymentConfirmation.verifiedAt,
      rejectionReason: paymentConfirmation.rejectionReason,
      createdAt: paymentConfirmation.createdAt
    };

    sendSuccess(res, 'Payment confirmation retrieved successfully', responseData);
  } catch (error) {
    console.error('Get payment confirmation error:', error);
    sendError(res, (error as Error).message);
  }
});

// PUT /api/v2/payment-confirmations/:id - Update payment confirmation
router.put(
  '/:id',
  authenticate,
  UploadController.cloudinaryUploadSingle('proofImage'),
  async (req: AuthenticatedRequest, res: Response) => {
    try {
      if (!req.user) {
        sendError(res, 'Authentication required', undefined, 401);
        return;
      }

      const { id } = req.params;
      const { amount, paymentMethod, notes } = req.body;

      const paymentConfirmation = await PaymentConfirmation.findOne({
        _id: id,
        user: req.user._id
      });

      if (!paymentConfirmation) {
        sendError(res, 'Payment confirmation not found', undefined, 404);
        return;
      }

      // Only allow updates to pending confirmations
      if (paymentConfirmation.status !== 'pending') {
        sendError(res, 'Cannot update verified or rejected payment confirmations', undefined, 400);
        return;
      }

      // Update fields
      if (amount) paymentConfirmation.amount = parseFloat(amount);
      if (paymentMethod) paymentConfirmation.paymentMethod = paymentMethod;
      if (notes !== undefined) paymentConfirmation.notes = notes;
      if ((req as any).file) paymentConfirmation.proofImage = (req as any).file.path;

      await paymentConfirmation.save();

      const responseData = {
        id: paymentConfirmation._id,
        amount: paymentConfirmation.amount,
        paymentMethod: paymentConfirmation.paymentMethod,
        proofImage: paymentConfirmation.proofImage,
        notes: paymentConfirmation.notes,
        status: paymentConfirmation.status,
        updatedAt: paymentConfirmation.updatedAt
      };

      sendSuccess(res, 'Payment confirmation updated successfully', responseData);
    } catch (error) {
      console.error('Update payment confirmation error:', error);
      sendError(res, (error as Error).message);
    }
  }
);

// DELETE /api/v2/payment-confirmations/:id - Delete payment confirmation
router.delete('/:id', authenticate, async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (!req.user) {
      sendError(res, 'Authentication required', undefined, 401);
      return;
    }

    const { id } = req.params;

    const paymentConfirmation = await PaymentConfirmation.findOne({
      _id: id,
      user: req.user._id
    });

    if (!paymentConfirmation) {
      sendError(res, 'Payment confirmation not found', undefined, 404);
      return;
    }

    // Only allow deletion of pending confirmations
    if (paymentConfirmation.status !== 'pending') {
      sendError(res, 'Cannot delete verified or rejected payment confirmations', undefined, 400);
      return;
    }

    await PaymentConfirmation.findByIdAndDelete(id);

    sendSuccess(res, 'Payment confirmation deleted successfully', { id });
  } catch (error) {
    console.error('Delete payment confirmation error:', error);
    sendError(res, (error as Error).message);
  }
});

export default router;
