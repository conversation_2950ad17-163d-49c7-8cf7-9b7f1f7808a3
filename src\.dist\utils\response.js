"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.sendServerError = exports.sendForbidden = exports.sendUnauthorized = exports.sendNotFound = exports.sendCreated = exports.sendError = exports.sendSuccess = exports.sendResponse = void 0;
const sendResponse = (res, statusCode, success, message, data, error) => {
    const response = {
        success,
        message,
        ...(data && { data }),
        ...(error && { error })
    };
    return res.status(statusCode).json(response);
};
exports.sendResponse = sendResponse;
const sendSuccess = (res, message, data, statusCode = 200) => {
    return (0, exports.sendResponse)(res, statusCode, true, message, data);
};
exports.sendSuccess = sendSuccess;
const sendError = (res, message, error, statusCode = 400) => {
    return (0, exports.sendResponse)(res, statusCode, false, message, undefined, error);
};
exports.sendError = sendError;
const sendCreated = (res, message, data) => {
    return (0, exports.sendSuccess)(res, message, data, 201);
};
exports.sendCreated = sendCreated;
const sendNotFound = (res, message = 'Resource not found') => {
    return (0, exports.sendError)(res, message, undefined, 404);
};
exports.sendNotFound = sendNotFound;
const sendUnauthorized = (res, message = 'Unauthorized access') => {
    return (0, exports.sendError)(res, message, undefined, 401);
};
exports.sendUnauthorized = sendUnauthorized;
const sendForbidden = (res, message = 'Access forbidden') => {
    return (0, exports.sendError)(res, message, undefined, 403);
};
exports.sendForbidden = sendForbidden;
const sendServerError = (res, message = 'Internal server error', error) => {
    return (0, exports.sendError)(res, message, error, 500);
};
exports.sendServerError = sendServerError;
