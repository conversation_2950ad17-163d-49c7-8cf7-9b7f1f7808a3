"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReferralSettings = exports.Referral = void 0;
const mongoose_1 = __importStar(require("mongoose"));
const referralSchema = new mongoose_1.Schema({
    referrer: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    referralCode: {
        type: String,
        required: true,
        uppercase: true,
        trim: true
    },
    referredUsers: [{
            user: {
                type: mongoose_1.Schema.Types.ObjectId,
                ref: 'User',
                required: true
            },
            dateReferred: {
                type: Date,
                default: Date.now
            },
            status: {
                type: String,
                enum: ['pending', 'completed'],
                default: 'pending'
            },
            rewardEarned: {
                type: Number,
                default: 0
            }
        }],
    totalReferrals: {
        type: Number,
        default: 0
    },
    totalRewards: {
        type: Number,
        default: 0
    },
    isActive: {
        type: Boolean,
        default: true
    }
}, {
    timestamps: true
});
const referralSettingsSchema = new mongoose_1.Schema({
    referrerReward: {
        type: Number,
        required: true,
        default: 10
    },
    referredReward: {
        type: Number,
        required: true,
        default: 5
    },
    minimumPurchase: {
        type: Number,
        required: true,
        default: 50
    },
    maxRewards: {
        type: Number,
        default: null
    },
    isActive: {
        type: Boolean,
        default: true
    }
}, {
    timestamps: true
});
// Generate referral code before saving
referralSchema.pre('save', function (next) {
    if (!this.referralCode) {
        this.referralCode = 'REF' + Date.now().toString().slice(-6) + Math.random().toString(36).substring(2, 5).toUpperCase();
    }
    next();
});
// Index for better query performance
referralSchema.index({ referrer: 1 }, { unique: true });
referralSchema.index({ referralCode: 1 }, { unique: true });
referralSchema.index({ isActive: 1 });
exports.Referral = mongoose_1.default.model('Referral', referralSchema);
exports.ReferralSettings = mongoose_1.default.model('ReferralSettings', referralSettingsSchema);
