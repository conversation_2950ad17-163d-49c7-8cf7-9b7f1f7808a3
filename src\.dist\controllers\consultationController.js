"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConsultationController = void 0;
const consultationService_1 = require("../services/consultationService");
const response_1 = require("../utils/response");
class ConsultationController {
    /**
     * Smart consultation booking endpoint
     * Handles guest users, existing users, and logged-in users seamlessly
     */
    static async bookConsultation(req, res) {
        try {
            const { name, firstName, lastName, email, phone, service, date, time, message } = req.body;
            // Validate required fields - either name OR (firstName AND lastName)
            if ((!name && (!firstName || !lastName)) || !email || !service || !date || !time) {
                (0, response_1.sendError)(res, 'Name (or first name and last name), email, service, date, and time are required');
                return;
            }
            // Check if user is authenticated (optional)
            const authReq = req;
            const isLoggedIn = !!authReq.user;
            const userId = authReq.user?._id;
            const consultationData = {
                name: name?.trim(),
                firstName: firstName?.trim(),
                lastName: lastName?.trim(),
                email: email.trim().toLowerCase(),
                phone: phone?.trim(),
                service,
                date,
                time,
                message: message?.trim(),
                isLoggedIn,
                userId
            };
            const result = await consultationService_1.ConsultationService.bookConsultation(consultationData);
            // Send appropriate response based on whether it's a new user or existing user
            if (result.isNewUser) {
                (0, response_1.sendCreated)(res, result.message, {
                    appointment: result.appointment,
                    user: result.user,
                    token: result.token,
                    refreshToken: result.refreshToken,
                    isNewUser: true,
                    // Note: In production, send password via email instead of response
                    temporaryPassword: result.generatedPassword
                });
            }
            else {
                (0, response_1.sendCreated)(res, result.message, {
                    appointment: result.appointment,
                    user: result.user,
                    isNewUser: false
                });
            }
        }
        catch (error) {
            console.error('Book consultation error:', error);
            // Handle specific error cases
            const errorMessage = error.message;
            if (errorMessage.includes('time slot is already booked')) {
                (0, response_1.sendError)(res, 'This time slot is already booked. Please choose another time.');
            }
            else if (errorMessage.includes('Service not found')) {
                (0, response_1.sendError)(res, 'Selected service is not available.');
            }
            else if (errorMessage.includes('email already exists')) {
                (0, response_1.sendError)(res, 'An account with this email already exists. Please log in first.');
            }
            else {
                (0, response_1.sendError)(res, 'Failed to book consultation. Please try again.');
            }
        }
    }
    /**
     * Get available consultation time slots
     */
    static async getAvailability(req, res) {
        try {
            const { date, service } = req.query;
            if (!date) {
                (0, response_1.sendError)(res, 'Date parameter is required');
                return;
            }
            const availableSlots = await consultationService_1.ConsultationService.getAvailability(date, service);
            (0, response_1.sendSuccess)(res, 'Available consultation slots retrieved successfully', {
                date,
                service,
                availableSlots
            });
        }
        catch (error) {
            console.error('Get consultation availability error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    /**
     * Guest consultation booking (public endpoint)
     * This is the main endpoint for the consultation form
     * Now supports password authentication for existing users
     */
    static async bookGuestConsultation(req, res) {
        try {
            const { name, firstName, lastName, email, phone, password, service, date, time, message } = req.body;
            // Validate required fields - either name OR (firstName AND lastName)
            if ((!name && (!firstName || !lastName)) || !email || !service || !date || !time) {
                (0, response_1.sendError)(res, 'Name (or first name and last name), email, service, date, and time are required');
                return;
            }
            // Validate email format
            const emailRegex = /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/;
            if (!emailRegex.test(email)) {
                (0, response_1.sendError)(res, 'Please enter a valid email address');
                return;
            }
            // Validate phone format if provided
            if (phone && !/^[\+]?[1-9][\d]{0,15}$/.test(phone)) {
                (0, response_1.sendError)(res, 'Please enter a valid phone number');
                return;
            }
            const consultationData = {
                name: name?.trim(),
                firstName: firstName?.trim(),
                lastName: lastName?.trim(),
                email: email.trim().toLowerCase(),
                phone: phone?.trim(),
                password: password?.trim(), // Include password for guest authentication
                service,
                date,
                time,
                message: message?.trim(),
                isLoggedIn: false
            };
            const result = await consultationService_1.ConsultationService.bookConsultation(consultationData);
            (0, response_1.sendCreated)(res, result.message, {
                appointment: result.appointment,
                user: {
                    name: result.user.name,
                    email: result.user.email,
                    phone: result.user.phone
                },
                isNewUser: result.isNewUser,
                // Include auth tokens for automatic login if new user
                ...(result.isNewUser && {
                    token: result.token,
                    refreshToken: result.refreshToken,
                    message: 'Account created and consultation booked! You are now logged in.'
                })
            });
        }
        catch (error) {
            console.error('Book guest consultation error:', error);
            const errorMessage = error.message;
            if (errorMessage.includes('time slot is already booked')) {
                (0, response_1.sendError)(res, 'This time slot is already booked. Please choose another time.');
            }
            else if (errorMessage.includes('Service not found')) {
                (0, response_1.sendError)(res, 'Selected service is not available.');
            }
            else {
                (0, response_1.sendError)(res, 'Failed to book consultation. Please try again.');
            }
        }
    }
}
exports.ConsultationController = ConsultationController;
