"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MediaService = void 0;
const Media_1 = require("../models/Media");
const cloudinaryService_1 = require("./cloudinaryService");
class MediaService {
    /**
     * Upload media file
     */
    static async uploadMedia(file, uploadedBy, metadata) {
        try {
            // Upload to Cloudinary
            const uploadResult = await (0, cloudinaryService_1.uploadBufferToCloudinary)(file.buffer, {
                folder: 'media-library',
                resource_type: 'auto'
            });
            // Create media record
            const media = new Media_1.Media({
                filename: uploadResult.public_id,
                originalName: file.originalname,
                mimeType: file.mimetype,
                size: file.size,
                url: uploadResult.secure_url,
                cloudinaryId: uploadResult.public_id,
                uploadedBy,
                alt: metadata?.alt || '',
                caption: metadata?.caption || '',
                description: metadata?.description || '',
                tags: metadata?.tags || [],
                usedIn: []
            });
            return await media.save();
        }
        catch (error) {
            console.error('Error uploading media:', error);
            throw new Error('Failed to upload media file');
        }
    }
    /**
     * Get media library with pagination and filters
     */
    static async getMediaLibrary(params) {
        const { page = 1, limit = 20, search, mimeType, tags, uploadedBy } = params;
        const filter = { isActive: true };
        // Search filter
        if (search) {
            filter.$or = [
                { originalName: { $regex: search, $options: 'i' } },
                { alt: { $regex: search, $options: 'i' } },
                { caption: { $regex: search, $options: 'i' } },
                { description: { $regex: search, $options: 'i' } },
                { tags: { $in: [new RegExp(search, 'i')] } }
            ];
        }
        // MIME type filter
        if (mimeType) {
            if (mimeType === 'image') {
                filter.mimeType = { $regex: '^image/', $options: 'i' };
            }
            else if (mimeType === 'video') {
                filter.mimeType = { $regex: '^video/', $options: 'i' };
            }
            else if (mimeType === 'document') {
                filter.mimeType = { $regex: '^application/', $options: 'i' };
            }
            else {
                filter.mimeType = mimeType;
            }
        }
        // Tags filter
        if (tags && tags.length > 0) {
            filter.tags = { $in: tags };
        }
        // Uploaded by filter
        if (uploadedBy) {
            filter.uploadedBy = uploadedBy;
        }
        const skip = (page - 1) * limit;
        const [media, total] = await Promise.all([
            Media_1.Media.find(filter)
                .populate('uploadedBy', 'firstName lastName name email')
                .sort({ uploadedAt: -1 })
                .skip(skip)
                .limit(limit),
            Media_1.Media.countDocuments(filter)
        ]);
        return {
            media,
            pagination: {
                currentPage: page,
                totalPages: Math.ceil(total / limit),
                totalItems: total,
                itemsPerPage: limit,
                hasNextPage: page < Math.ceil(total / limit),
                hasPrevPage: page > 1
            }
        };
    }
    /**
     * Get media by ID
     */
    static async getMediaById(id) {
        return await Media_1.Media.findById(id)
            .populate('uploadedBy', 'firstName lastName name email');
    }
    /**
     * Update media metadata
     */
    static async updateMedia(id, updateData) {
        return await Media_1.Media.findByIdAndUpdate(id, updateData, { new: true }).populate('uploadedBy', 'firstName lastName name email');
    }
    /**
     * Delete media
     */
    static async deleteMedia(id) {
        try {
            const media = await Media_1.Media.findById(id);
            if (!media) {
                return false;
            }
            // Delete from Cloudinary
            if (media.cloudinaryId) {
                await (0, cloudinaryService_1.deleteFromCloudinary)(media.cloudinaryId);
            }
            // Soft delete (mark as inactive)
            await Media_1.Media.findByIdAndUpdate(id, { isActive: false });
            return true;
        }
        catch (error) {
            console.error('Error deleting media:', error);
            return false;
        }
    }
    /**
     * Track media usage
     */
    static async trackMediaUsage(mediaId, usage) {
        await Media_1.Media.findByIdAndUpdate(mediaId, {
            $addToSet: {
                usedIn: usage
            }
        });
    }
    /**
     * Remove media usage tracking
     */
    static async removeMediaUsage(mediaId, usage) {
        await Media_1.Media.findByIdAndUpdate(mediaId, {
            $pull: {
                usedIn: usage
            }
        });
    }
    /**
     * Get media usage statistics
     */
    static async getMediaStats() {
        const stats = await Media_1.Media.aggregate([
            { $match: { isActive: true } },
            {
                $group: {
                    _id: null,
                    totalFiles: { $sum: 1 },
                    totalSize: { $sum: '$size' },
                    imageCount: {
                        $sum: {
                            $cond: [{ $regexMatch: { input: '$mimeType', regex: '^image/' } }, 1, 0]
                        }
                    },
                    videoCount: {
                        $sum: {
                            $cond: [{ $regexMatch: { input: '$mimeType', regex: '^video/' } }, 1, 0]
                        }
                    },
                    documentCount: {
                        $sum: {
                            $cond: [{ $regexMatch: { input: '$mimeType', regex: '^application/' } }, 1, 0]
                        }
                    }
                }
            }
        ]);
        return stats[0] || {
            totalFiles: 0,
            totalSize: 0,
            imageCount: 0,
            videoCount: 0,
            documentCount: 0
        };
    }
    /**
     * Get all unique tags
     */
    static async getAllTags() {
        const result = await Media_1.Media.aggregate([
            { $match: { isActive: true } },
            { $unwind: '$tags' },
            { $group: { _id: '$tags' } },
            { $sort: { _id: 1 } }
        ]);
        return result.map(item => item._id);
    }
    /**
     * Bulk delete media
     */
    static async bulkDeleteMedia(ids) {
        let deletedCount = 0;
        for (const id of ids) {
            const success = await this.deleteMedia(id);
            if (success)
                deletedCount++;
        }
        return deletedCount;
    }
}
exports.MediaService = MediaService;
