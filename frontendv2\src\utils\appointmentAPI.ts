import { API_BASE_URL } from './api';

// Utility function to convert 12-hour time to 24-hour format
function convertTo24Hour(time12h: string): string {
  const [time, modifier] = time12h.split(' ');
  let [hours, minutes] = time.split(':');

  if (hours === '12') {
    hours = '00';
  }

  if (modifier === 'PM') {
    hours = (parseInt(hours, 10) + 12).toString();
  }

  return `${hours.padStart(2, '0')}:${minutes}`;
}

export interface CreateAppointmentRequest {
  serviceId: string;
  serviceName: string;
  servicePrice: number;
  addOns: Array<{
    id: string;
    name: string;
    price: number;
    duration: number;
  }>;
  date: string;
  time: string;
  customerInfo: {
    firstName: string;
    lastName: string;
    email: string;
    phone?: string;
    paymentMethod: 'cashapp' | 'zelle';
    paymentDetails: string;
  };
  totalPrice: number;
}

export interface Appointment {
  id: string;
  userId?: string;
  serviceId: string;
  serviceName: string;
  servicePrice: number;
  addOns: Array<{
    id: string;
    name: string;
    price: number;
    duration: number;
  }>;
  date: string;
  time: string;
  status: 'pending' | 'confirmed' | 'completed' | 'cancelled';
  customerInfo: {
    firstName: string;
    lastName: string;
    email: string;
    phone?: string;
    paymentMethod: 'cashapp' | 'zelle';
    paymentDetails: string;
  };
  totalPrice: number;
  createdAt: string;
  updatedAt: string;
}

export const appointmentAPI = {
  // Create a new appointment (public endpoint)
  async createAppointment(appointmentData: CreateAppointmentRequest): Promise<Appointment> {
    // Transform data to match backend expectations
    const backendData = {
      serviceId: appointmentData.serviceId,
      date: appointmentData.date,
      time: convertTo24Hour(appointmentData.time), // Convert 12-hour to 24-hour format
      customerInfo: {
        firstName: appointmentData.customerInfo.firstName,
        lastName: appointmentData.customerInfo.lastName,
        email: appointmentData.customerInfo.email,
        phone: appointmentData.customerInfo.phone ? `+1${appointmentData.customerInfo.phone.replace(/\D/g, '')}` : undefined // Format phone with +1 prefix
      },
      addOns: appointmentData.addOns || [],
      userId: undefined, // Will be set if user is logged in
      notes: '' // Add notes field if needed
    };

    console.log('Sending appointment data:', backendData);

    const response = await fetch(`${API_BASE_URL}/appointments`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
        // Removed authorization header - endpoint is now public
      },
      body: JSON.stringify(backendData)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to create appointment');
    }

    return response.json();
  },

  // Get all appointments (admin only)
  async getAppointments(): Promise<Appointment[]> {
    const token = localStorage.getItem('authToken') || localStorage.getItem('token');
    const response = await fetch(`${API_BASE_URL}/appointments`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch appointments');
    }

    const result = await response.json();

    console.log(result)

    // Handle different response formats
    if (result && result.success && Array.isArray(result.data)) {
      return result.data;


    } else if (Array.isArray(result)) {
      return result;
    } else if (result && Array.isArray(result.appointments)) {
      return result.appointments;

    } else {
      console.warn('Unexpected response format:', result);
      return [];
    }
  },

  // Get user's appointments
  async getUserAppointments(): Promise<Appointment[]> {
    const token = localStorage.getItem('authToken') || localStorage.getItem('token');
    const response = await fetch(`${API_BASE_URL}/appointments/user`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch user appointments');
    }

    const result = await response.json();

    // Handle different response formats
    if (result && result.success && Array.isArray(result.data)) {
      return result.data;
    } else if (Array.isArray(result)) {
      return result;
    } else if (result && Array.isArray(result.appointments)) {
      return result.appointments;
    } else {
      console.warn('Unexpected response format:', result);
      return [];
    }
  },

  // Update appointment status (admin only)
  async updateAppointmentStatus(appointmentId: string, status: Appointment['status']): Promise<Appointment> {
    const token = localStorage.getItem('authToken') || localStorage.getItem('token');
    const response = await fetch(`${API_BASE_URL}/appointments/${appointmentId}/status`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({ status })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to update appointment status');
    }

    return response.json();
  },

  // Get appointment by ID
  async getAppointment(appointmentId: string): Promise<Appointment> {
    const token = localStorage.getItem('authToken') || localStorage.getItem('token');
    const response = await fetch(`${API_BASE_URL}/appointments/${appointmentId}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch appointment');
    }

    return response.json();
  },

  // Cancel appointment
  async cancelAppointment(appointmentId: string): Promise<Appointment> {
    const token = localStorage.getItem('authToken') || localStorage.getItem('token');
    const response = await fetch(`${API_BASE_URL}/appointments/${appointmentId}/cancel`, {
      method: 'PATCH',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to cancel appointment');
    }

    return response.json();
  },

  // Get available time slots for a date
  async getAvailableTimeSlots(date: string): Promise<string[]> {
    const token = localStorage.getItem('authToken') || localStorage.getItem('token');
    const response = await fetch(`${API_BASE_URL}/appointments/available-slots?date=${date}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch available time slots');
    }

    const result = await response.json();

    // Handle different response formats
    if (result && result.success && Array.isArray(result.data)) {
      return result.data;
    } else if (Array.isArray(result)) {
      return result;
    } else if (result && Array.isArray(result.slots)) {
      return result.slots;
    } else {
      console.warn('Unexpected response format for time slots:', result);
      return [];
    }
  }
};
