"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const controllers_1 = require("../controllers");
const auth_1 = require("../middleware/auth");
const validation_1 = require("../middleware/validation");
const validation_2 = require("../utils/validation");
const router = (0, express_1.Router)();
// GET /api/content/legal/:type
router.get('/legal/:type', controllers_1.ContentController.getLegalContent);
// PUT /api/content/legal/:type (admin only)
router.put('/legal/:type', auth_1.authenticate, (0, auth_1.authorize)('admin'), controllers_1.ContentController.updateLegalContent);
// GET /api/content/faq
router.get('/faq', controllers_1.ContentController.getFAQContent);
// PUT /api/content/faq (admin only)
router.put('/faq', auth_1.authenticate, (0, auth_1.authorize)('admin'), controllers_1.ContentController.updateFAQContent);
// POST /api/content/faq (admin only)
router.post('/faq', auth_1.authenticate, (0, auth_1.authorize)('admin'), controllers_1.ContentController.createFAQ);
// PUT /api/content/faq/:id (admin only)
router.put('/faq/:id', auth_1.authenticate, (0, auth_1.authorize)('admin'), (0, validation_1.validate)((0, validation_2.mongoIdValidation)()), controllers_1.ContentController.updateFAQ);
// DELETE /api/content/faq/:id (admin only)
router.delete('/faq/:id', auth_1.authenticate, (0, auth_1.authorize)('admin'), (0, validation_1.validate)((0, validation_2.mongoIdValidation)()), controllers_1.ContentController.deleteFAQ);
exports.default = router;
